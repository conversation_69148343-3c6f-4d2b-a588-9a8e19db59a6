`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Chat AI - Single Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            perspective: 1000px; /* For 3D effects */
        }

        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            background-attachment: fixed;
            overflow-x: hidden;
            position: relative;
        }

        .parallax-bg {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            z-index: 0;
            pointer-events: none;
            background: radial-gradient(circle at 60% 40%, #00ffcc33 0%, transparent 70%),
                        radial-gradient(circle at 30% 70%, #00b7eb22 0%, transparent 80%);
            will-change: background-position;
            transition: background-position 0.2s;
        }

        body.dark-mode {
            background: #121212;
            color: #e0e0e0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            transform-style: preserve-3d;
            transition: transform 0.5s ease;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #00ffcc;
            text-shadow: 0 0 10px rgba(0, 255, 204, 0.5);
            transform-style: preserve-3d;
        }

        .step-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            font-size: 1.1rem;
            font-weight: bold;
            text-shadow: 0 0 8px #00ffcc, 0 0 2px #fff;
        }

        .table-name {
            font-weight: bold;
            color: #00ffcc;
            margin-bottom: 5px;
            text-shadow: 0 0 8px #00ffcc, 0 0 2px #fff;
        }

        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
            min-height: 600px;
            transform-style: preserve-3d;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: fit-content;
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
        }

        .content-area {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-height: 600px;
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
        }

        .step-section {
            margin-bottom: 25px;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .step-section.active {
            background: rgba(0, 255, 204, 0.1);
            border-color: #00ffcc;
        }

        .step-section.completed {
            background: rgba(0, 255, 0, 0.1);
            border-color: #00ff00;
        }

        .step-section.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .step-section.active .step-number {
            background: #00ffcc;
            color: #1a1a2e;
        }

        .step-section.completed .step-number {
            background: #00ff00;
            color: #1a1a2e;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #00ffcc;
            font-size: 0.9rem;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #e0e0e0;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #00ffcc;
            box-shadow: 0 0 8px rgba(0, 255, 204, 0.3);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(90deg, #00ffcc, #00b7eb);
            color: #1a1a2e;
            box-shadow: 0 2px 8px #00ffcc80, 0 1.5px 8px rgba(0,0,0,0.12);
            text-shadow: 0 0 4px #00ffcc;
        }

        .btn-primary:hover:not(:disabled) {
            background: linear-gradient(90deg, #00b7eb, #00ffcc);
            box-shadow: 0 0 12px rgba(0, 255, 204, 0.5);
        }

        .btn-primary:active {
            transform: translateY(2px) scale(0.98);
            box-shadow: 0 1px 2px #00ffcc40;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e0e0e0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.2);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            transition: background-color 0.3s ease;
        }

        .status-dot.connected {
            background: #00ff00;
        }

        .status-dot.connecting {
            background: #ffaa00;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .content-panel {
            display: none;
        }

        .content-panel.active {
            display: block;
        }

        .schema-browser {
            max-height: 300px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .table-item {
            padding: 10px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 24px rgba(0,255,204,0.10), 0 1.5px 8px rgba(0,0,0,0.12);
        }

        .table-item:hover {
            background: rgba(0, 255, 204, 0.1);
            transform: translateY(-8px) scale(1.03) rotateY(4deg);
            box-shadow: 0 12px 40px rgba(0,255,204,0.18), 0 4px 16px rgba(0,0,0,0.18);
            z-index: 2;
        }

        .table-info {
            font-size: 0.8rem;
            color: #888;
        }

        .query-area {
            margin-bottom: 20px;
        }

        .query-input {
            width: 100%;
            min-height: 100px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #e0e0e0;
            resize: vertical;
            font-family: inherit;
        }

        .results-area {
            max-height: 400px;
            overflow: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 15px;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .results-table th,
        .results-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .results-table th {
            background: rgba(0, 255, 204, 0.1);
            color: #00ffcc;
            position: sticky;
            top: 0;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #888;
        }

        .error {
            color: #ff5252;
            background: rgba(255, 82, 82, 0.1);
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .success {
            color: #00ff00;
            background: rgba(0, 255, 0, 0.1);
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                order: 2;
            }
        }

        @media (max-width: 480px) {
    .main-content, .container {
        padding: 0 !important;
    }
    .sidebar, .content-area {
        min-height: 200px;
        padding: 8px;
    }
    h1 {
        font-size: 1.5rem;
    }
}

        .schema-sidebar {
            position: fixed;
            right: 0;
            top: 60px;
            width: 250px;
            height: calc(100vh - 60px);
            background-color: #f8f9fa;
            border-left: 1px solid #dee2e6;
            padding: 15px;
            overflow-y: auto;
            z-index: 100;
        }

        .schema-header {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .schema-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        #selected-database-name {
            display: block;
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }

        .schema-content {
            font-size: 14px;
        }

        .schema-table {
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }

        .schema-table-header {
            background-color: #e9ecef;
            padding: 8px 12px;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
            cursor: pointer;
        }

        .schema-table-columns {
            padding: 8px 12px;
            background-color: white;
        }

        .schema-column {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .schema-column:last-child {
            border-bottom: none;
        }

        .schema-column-name {
            font-weight: 500;
        }

        .schema-column-type {
            color: #6c757d;
            font-size: 12px;
        }

        .schema-placeholder {
            color: #6c757d;
            text-align: center;
            padding: 20px 0;
        }

        /* Adjust content area to make room for sidebar */
        .content-area {
            margin-right: 250px;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .schema-sidebar {
                width: 200px;
            }
            .content-area {
                margin-right: 200px;
            }
        }

        @media (max-width: 768px) {
            .schema-sidebar {
                position: static;
                width: 100%;
                height: auto;
                border-left: none;
                border-bottom: 1px solid #dee2e6;
                margin-bottom: 15px;
            }
            .content-area {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="parallax-bg"></div>
    <div class="container">
        <h1>SQL Chat AI</h1>
        
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-indicator">
                <span class="status-dot" id="status-dot"></span>
                <span id="status-text">Ready to connect</span>
            </div>
            <div id="connection-info"></div>
        </div>

        <div class="main-content">
            <!-- Sidebar with Steps -->
            <div class="sidebar">
                <!-- Step 1: Database Type -->
                <div class="step-section active" id="step-1">
                    <div class="step-header">
                        <span class="step-number">1</span>
                        <span>Database Type</span>
                    </div>
                    <div class="form-group">
                        <label for="db-type">Select Database Type</label>
                        <select id="db-type" class="form-control">
                            <option value="">Choose database...</option>
                            <option value="mysql">MySQL</option>
                            <option value="postgresql">PostgreSQL</option>
                            <option value="mongodb">MongoDB</option>
                            <option value="sqlite">SQLite</option>
                        </select>
                    </div>
                </div>

                <!-- Step 2: Connection Details -->
                <div class="step-section disabled" id="step-2">
                    <div class="step-header">
                        <span class="step-number">2</span>
                        <span>Connection</span>
                    </div>
                    <div id="connection-fields"></div>
                    <button id="connect-btn" class="btn btn-primary" disabled>Connect</button>
                </div>

                <!-- Step 3: Database Selection -->
                <div class="step-section disabled" id="step-3">
                    <div class="step-header">
                        <span class="step-number">3</span>
                        <span>Select Database</span>
                    </div>
                    <div class="form-group">
                        <label for="database-select">Available Databases</label>
                        <select id="database-select" class="form-control">
                            <option value="">Select database...</option>
                        </select>
                    </div>
                </div>

                <!-- Step 4: Query Interface -->
                <div class="step-section disabled" id="step-4">
                    <div class="step-header">
                        <span class="step-number">4</span>
                        <span>Query</span>
                    </div>
                    <div class="query-area">
                        <textarea 
                            id="query-input" 
                            class="query-input" Can we add 3d effects and parallax effects
Also give dark and, style look also screen size adjustable with 30 to 144 hz refresh rate screen
                            placeholder="Ask a question in natural language...
Example: 'Show me all users who registered last month'"
                        ></textarea>
                        <button id="execute-btn" class="btn btn-primary">Execute Query</button>
                        <button id="clear-btn" class="btn btn-secondary">Clear</button>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="content-area">
                <!-- Welcome Panel -->
                <div class="content-panel active" id="welcome-panel">
                    <h2>Welcome to SQL Chat AI</h2>
                    <p>Follow these simple steps to get started:</p>
                    <ol style="margin: 20px 0; padding-left: 20px;">
                        <li>Select your database type</li>
                        <li>Enter connection details</li>
                        <li>Choose a database to work with</li>
                        <li>Ask questions in natural language</li>
                    </ol>
                    <p>Start by selecting a database type from the sidebar.</p>
                </div>

                <!-- Connection Panel -->
                <div class="content-panel" id="connection-panel">
                    <h2>Database Connection</h2>
                    <div id="connection-status"></div>
                </div>

                <!-- Schema Panel -->
                <div class="content-panel" id="schema-panel">
                    <h2>Database Schema</h2>
                    <div id="selected-database-name"></div>
                    <div class="schema-browser" id="schema-display">
                        <div class="loading">Loading schema...</div>
                    </div>
                    <h3>Sample Data</h3>
                    <div class="results-area" id="sample-data">
                        <div class="loading">Select a table to view sample data</div>
                    </div>
                </div>

                <!-- Results Panel -->
                <div class="content-panel" id="results-panel">
                    <h2>Query Results</h2>
                    <div id="query-info"></div>
                    <div class="results-area" id="query-results">
                        <div class="loading">Execute a query to see results</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SQLChatApp {
            constructor() {
                this.currentConnection = null;
                this.apiBase = 'http://localhost:3002/api';
                this.currentStep = 1;
                this.schema = null;
                this.selectedDatabase = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.showStatus('Ready to connect', 'ready');
                console.log('SQL Chat App initialized');
            }

            setupEventListeners() {
                // Database type selection
                document.getElementById('db-type').addEventListener('change', (e) => {
                    this.handleDatabaseTypeChange(e.target.value);
                });

                // Connect button
                document.getElementById('connect-btn').addEventListener('click', () => {
                    this.handleConnect();
                });

                // Database selection
                document.getElementById('database-select').addEventListener('change', (e) => {
                    this.handleDatabaseSelect(e.target.value);
                });

                // Query execution
                document.getElementById('execute-btn').addEventListener('click', () => {
                    this.handleQueryExecute();
                });

                // Clear button
                document.getElementById('clear-btn').addEventListener('click', () => {
                    this.handleClear();
                });
            }

            // Step 1: Database Type Selection
            handleDatabaseTypeChange(dbType) {
                if (!dbType) return;

                console.log('Database type selected:', dbType);
                this.loadConnectionFields(dbType);
                this.activateStep(2);
                this.showPanel('connection-panel');
            }

            loadConnectionFields(dbType) {
                const fieldsContainer = document.getElementById('connection-fields');
                
                const fieldConfigs = {
                    mysql: [
                        { name: 'host', label: 'Host', type: 'text', default: 'localhost' },
                        { name: 'port', label: 'Port', type: 'number', default: '3306' },
                        { name: 'username', label: 'Username', type: 'text', default: 'root' },
                        { name: 'password', label: 'Password', type: 'password', default: '' }
                    ],
                    postgresql: [
                        { name: 'host', label: 'Host', type: 'text', default: 'localhost' },
                        { name: 'port', label: 'Port', type: 'number', default: '5432' },
                        { name: 'username', label: 'Username', type: 'text', default: 'postgres' },
                        { name: 'password', label: 'Password', type: 'password', default: '' }
                    ],
                    mongodb: [
                        { name: 'host', label: 'Host', type: 'text', default: 'localhost' },
                        { name: 'port', label: 'Port', type: 'number', default: '27017' },
                        { name: 'username', label: 'Username', type: 'text', default: '' },
                        { name: 'password', label: 'Password', type: 'password', default: '' }
                    ],
                    sqlite: [
                        { name: 'database_path', label: 'Database Path', type: 'text', default: './databases/default.db' }
                    ]
                };

                const fields = fieldConfigs[dbType] || [];
                fieldsContainer.innerHTML = fields.map(field => `
                    <div class="form-group">
                        <label for="field-${field.name}">${field.label}</label>
                        <input 
                            type="${field.type}" 
                            id="field-${field.name}" 
                            class="form-control" 
                            value="${field.default}"
                            placeholder="Enter ${field.label.toLowerCase()}"
                        >
                    </div>
                `).join('');

                document.getElementById('connect-btn').disabled = false;
            }

        // Step 2: Database Connection
        async handleConnect() {
            const dbType = document.getElementById('db-type').value;
            const connectBtn = document.getElementById('connect-btn');
            
            if (!this.validateConnectionForm(dbType)) return;

            connectBtn.disabled = true;
            connectBtn.textContent = 'Connecting...';
            this.showStatus('Connecting to database...', 'connecting');

            try {
                const config = this.getConnectionConfig(dbType);
                
                console.log('Sending request to:', `${this.apiBase}/database/connect`);
                console.log('Request payload:', { type: dbType, database: '', config });
                
                const response = await fetch(`${this.apiBase}/database/connect`, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        type: dbType,
                        database: 'test',
                        config: config
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);

                // Check if response is actually JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('Non-JSON response:', textResponse);
                    throw new Error(`Server returned ${contentType}: ${textResponse.substring(0, 200)}`);
                }

                const data = await response.json();
                console.log('Parsed response:', data);
                
                if (data.success) {
                    this.currentConnection = {
                        type: dbType,
                        sessionId: data.session_id
                    };
                    
                    this.showStatus('Connected! Loading databases...', 'connected');
                    this.completeStep(2);
                    await this.loadDatabases();
                    
                } else {
                    throw new Error(data.message || 'Connection failed');
                }
            } catch (error) {
                console.error('Connection error details:', error);
                
                if (error instanceof SyntaxError && error.message.includes('JSON.parse')) {
                    this.showStatus('Server returned invalid response. Check server logs.', 'error');
                } else if (error.name === 'TypeError' && error.message.includes('NetworkError')) {
                    this.showStatus('Network error: Please ensure Node.js server is running on port 3002', 'error');
                } else {
                    this.showStatus(`Connection error: ${error.message}`, 'error');
                }
            } finally {
                connectBtn.disabled = false;
                connectBtn.textContent = 'Connect';
            }
        }

            validateConnectionForm(dbType) {
                const requiredFields = {
                    mysql: ['host', 'port', 'username', 'password'],
                    postgresql: ['host', 'port', 'username', 'password'],
                    mongodb: ['host', 'port'],
                    sqlite: ['database_path']
                };

                const fields = requiredFields[dbType] || [];
                for (const field of fields) {
                    const input = document.getElementById(`field-${field}`);
                    if (!input || !input.value.trim()) {
                        this.showStatus(`Please fill in ${field.replace('_', ' ')}`, 'error');
                        return false;
                    }
                }
                return true;
            }

            getConnectionConfig(dbType) {
                const config = {};
                const fieldMap = {
                    mysql: ['host', 'port', 'username', 'password'],
                    postgresql: ['host', 'port', 'username', 'password'],
                    mongodb: ['host', 'port', 'username', 'password'],
                    sqlite: ['database_path']
                };

                const fields = fieldMap[dbType] || [];
                fields.forEach(field => {
                    const input = document.getElementById(`field-${field}`);
                    if (input && input.value) {
                        config[field] = field === 'port' ? parseInt(input.value) : input.value;
                    }
                });

                return config;
            }

            async loadDatabases() {
                try {
                    console.log('🔍 Loading databases for session:', this.currentConnection.sessionId);
                    
                    const url = `${this.apiBase}/databases?session_id=${this.currentConnection.sessionId}`;
                    console.log('📡 Fetching from:', url);
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    console.log('📥 Response status:', response.status);
                    console.log('📥 Response headers:', [...response.headers.entries()]);
                    
                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }
                    
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        const textResponse = await response.text();
                        console.error('❌ Non-JSON response:', textResponse);
                        throw new Error(`Expected JSON, got ${contentType}: ${textResponse.substring(0, 200)}`);
                    }
                    
                    const data = await response.json();
                    console.log('📊 Databases response:', data);
                    
                    if (data.success) {
                        const select = document.getElementById('database-select');
                        select.innerHTML = '<option value="">Select database...</option>';
                        
                        data.databases.forEach(db => {
                            const option = document.createElement('option');
                            option.value = db.name;
                            option.textContent = `${db.name} (${db.type})`;
                            select.appendChild(option);
                        });

                        this.activateStep(3);
                        this.showStatus(`Found ${data.databases.length} databases`, 'connected');
                        
                    } else {
                        throw new Error(data.message || 'Failed to load databases');
                    }
                } catch (error) {
                    console.error('❌ Error loading databases:', error);
                    
                    if (error instanceof TypeError && error.message.includes('NetworkError')) {
                        this.showStatus('Network error: Check if Node.js server is running on port 3002', 'error');
                    } else {
                        this.showStatus(`Error loading databases: ${error.message}`, 'error');
                    }
                }
            }

            // Step 3: Database Selection
            async handleDatabaseSelect(database) {
                if (!database) return;

                this.selectedDatabase = database;
                this.showStatus(`Loading schema for ${database}...`, 'connecting');

                try {
                    // Select database
                    const selectResponse = await fetch(`${this.apiBase}/select-database`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            session_id: this.currentConnection.sessionId,
                            database: database
                        })
                    });

                    const selectData = await selectResponse.json();
                    if (!selectData.success) {
                        throw new Error(selectData.message || 'Failed to select database');
                    }

                    // Get tables for the selected database
                    const tablesResponse = await fetch(`${this.apiBase}/tables?session_id=${this.currentConnection.sessionId}&database=${database}`);
                    const tablesData = await tablesResponse.json();
                    if (!tablesData.success) {
                        throw new Error(tablesData.message || 'Failed to load tables');
                    }

                    // Get schema
                    const schemaResponse = await fetch(`${this.apiBase}/schema?session_id=${this.currentConnection.sessionId}&database=${this.selectedDatabase}`);
                    const schemaData = await schemaResponse.json();
                    
                    if (schemaData.success) {
                        this.schema = schemaData.schema;
                        this.displaySchema(tablesData.tables, schemaData.schema);
                        this.completeStep(3);
                        this.activateStep(4);
                        this.showPanel('schema-panel');
                        this.showStatus(`Connected to ${database}`, 'connected');
                        
                    } else {
                        throw new Error(schemaData.message || 'Failed to load schema');
                    }
                } catch (error) {
                    console.error('Error selecting database:', error);
                    this.showStatus(`Error: ${error.message}`, 'error');
                }
            }

            displaySchema(tables, schema) {
                const schemaDisplay = document.getElementById('schema-display');
                const databaseName = document.getElementById('selected-database-name');
                
                // Set the database name
                databaseName.textContent = this.selectedDatabase;
                
                // Clear previous schema
                schemaDisplay.innerHTML = '';
                
                if (!tables || tables.length === 0) {
                    schemaDisplay.innerHTML = '<div class="schema-placeholder">No tables found</div>';
                    return;
                }
                
                // Create HTML for each table
                tables.forEach(table => {
                    const tableDiv = document.createElement('div');
                    tableDiv.className = 'schema-table';
                    
                    // Table header
                    const tableHeader = document.createElement('div');
                    tableHeader.className = 'schema-table-header';
                    tableHeader.textContent = table.name;
                    tableHeader.onclick = () => this.handleTableSelect(table.name);
                    tableDiv.appendChild(tableHeader);
                    
                    // Table columns
                    const columnsDiv = document.createElement('div');
                    columnsDiv.className = 'schema-table-columns';
                    
                    // Get columns for this table
                    const columns = schema[table.name]?.columns || [];
                    
                    if (columns.length === 0) {
                        columnsDiv.innerHTML = '<div class="schema-placeholder">No columns found</div>';
                    } else {
                        columns.forEach(column => {
                            const columnDiv = document.createElement('div');
                            columnDiv.className = 'schema-column';
                            
                            const nameSpan = document.createElement('span');
                            nameSpan.className = 'schema-column-name';
                            nameSpan.textContent = column;
                            
                            const typeSpan = document.createElement('span');
                            typeSpan.className = 'schema-column-type';
                            // Try to get column type if available
                            const columnInfo = schema[table.name]?.columnInfo?.[column];
                            typeSpan.textContent = columnInfo?.type || '';
                            
                            columnDiv.appendChild(nameSpan);
                            columnDiv.appendChild(typeSpan);
                            columnsDiv.appendChild(columnDiv);
                        });
                    }
                    
                    tableDiv.appendChild(columnsDiv);
                    schemaDisplay.appendChild(tableDiv);
                });
            }

            handleTableSelect(tableName) {
                this.selectedTable = tableName;
                
                // Highlight the selected table
                const tableDivs = document.querySelectorAll('.schema-table-header');
                tableDivs.forEach(div => {
                    if (div.textContent === tableName) {
                        div.classList.add('selected');
                    } else {
                        div.classList.remove('selected');
                    }
                });
                
                // Update the query input placeholder
                const queryInput = document.getElementById('query-input');
                queryInput.placeholder = `Ask a question about ${tableName} (e.g., "Show all records in ${tableName}")`;
                
                // Show a message
                this.showStatus(`Selected table: ${tableName}`, 'info');
            }

            async loadSampleData(tableName) {
                const sampleArea = document.getElementById('sample-data');
                sampleArea.innerHTML = '<div class="loading">Loading sample data...</div>';

                try {
                    const response = await fetch(`${this.apiBase}/table-data?session_id=${this.currentConnection.sessionId}&table=${tableName}&limit=5`);
                    const data = await response.json();
                    
                    if (data.success && data.data.length > 0) {
                        this.displayResults(data.data, sampleArea, `Sample from ${tableName}`);
                    } else {
                        sampleArea.innerHTML = '<div class="loading">No data available</div>';
                    }
                } catch (error) {
                    console.error('Error loading sample data:', error);
                    sampleArea.innerHTML = '<div class="error">Error loading sample data</div>';
                }
            }

            // Step 4: Query Execution
            async handleQueryExecute() {
                const queryInput = document.getElementById('query-input');
                const queryText = queryInput.value.trim();

                if (!queryText) {
                    this.displayError('Please enter a question');
                    return;
                }

                if (!this.currentConnection?.sessionId || !this.selectedDatabase || !queryText) {
                    this.displayError('Session ID, database, and question are required.');
                    return;
                }

                // Ensure we have the database type
                if (!this.selectedDbType) {
                    // Get it from the connection info if possible
                    this.selectedDbType = this.currentConnection.dbType || 'mysql'; // Default to mysql if unknown
                }

                const executeBtn = document.getElementById('execute-btn');
                const originalText = executeBtn.textContent;

                try {
                    executeBtn.disabled = true;
                    executeBtn.textContent = 'Processing...';
                    this.showStatus('Processing your question...', 'connecting');

                    console.log('🤖 Sending AI query:', {
                        question: queryText,
                        session_id: this.currentConnection.sessionId,
                        database: this.selectedDatabase,
                        db_type: this.selectedDbType,
                        table: this.selectedTable || null,
                        execute: true
                    });

                    const response = await fetch(`${this.apiBase}/ai-query`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            question: queryText,
                            session_id: this.currentConnection.sessionId,
                            database: this.selectedDatabase,
                            db_type: this.selectedDbType,
                            table: this.selectedTable || null,
                            execute: true
                        })
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }

                    const data = await response.json();
                    console.log('🤖 AI Query response:', data);

                    if (data.success) {
                        this.displayQueryResults(data);
                        this.showStatus(`Query executed successfully (${data.count || 0} results)`, 'connected');
                    } else {
                        this.displayError(data.error || data.message || 'Query failed');
                    }

                } catch (error) {
                    console.error('❌ Query error:', error);
                    this.displayError(error.message || 'Unknown error occurred');
                } finally {
                    executeBtn.disabled = false;
                    executeBtn.textContent = originalText;
                }
            }

            displayError(message) {
                this.showStatus(`❌ ${message}`, 'error');
            }

            displayError(message) {
                this.showStatus(`❌ ${message}`, 'error');
            }

            displayQueryResults(data) {
                const resultsContainer = document.getElementById('query-results');
                
                // Clear previous results
                resultsContainer.innerHTML = '';
                
                // Display the generated query
                const queryDiv = document.createElement('div');
                queryDiv.className = 'generated-query';
                
                const queryHeader = document.createElement('h4');
                queryHeader.textContent = 'Generated Query';
                queryDiv.appendChild(queryHeader);
                
                const queryCode = document.createElement('pre');
                queryCode.className = 'query-code';
                queryCode.textContent = data.generated_query;
                queryDiv.appendChild(queryCode);
                
                resultsContainer.appendChild(queryDiv);
                
                // Check if there are results
                if (!data.results || data.results.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'no-results';
                    noResults.textContent = 'No results found';
                    resultsContainer.appendChild(noResults);
                    return;
                }
                
                // Create results table
                const table = document.createElement('table');
                table.className = 'results-table';
                
                // Create table header
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');
                
                // Get column names from the first result
                const columns = Object.keys(data.results[0]);
                
                columns.forEach(column => {
                    const th = document.createElement('th');
                    th.textContent = column;
                    headerRow.appendChild(th);
                });
                
                thead.appendChild(headerRow);
                table.appendChild(thead);
                
                // Create table body
                const tbody = document.createElement('tbody');
                
                data.results.forEach(row => {
                    const tr = document.createElement('tr');
                    
                    columns.forEach(column => {
                        const td = document.createElement('td');
                        td.textContent = row[column] !== null ? row[column] : '';
                        tr.appendChild(td);
                    });
                    
                    tbody.appendChild(tr);
                });
                
                table.appendChild(tbody);
                
                // Add results info
                const resultsInfo = document.createElement('div');
                resultsInfo.className = 'results-info';
                resultsInfo.textContent = `Showing ${data.results.length} results`;
                
                resultsContainer.appendChild(resultsInfo);
                resultsContainer.appendChild(table);
                
                // Add CSS for the results
                const style = document.createElement('style');
                style.textContent = `
                    .generated-query {
                        margin-bottom: 20px;
                        padding: 10px;
                        background-color: #f8f9fa;
                        border-radius: 4px;
                    }
                    
                    .generated-query h4 {
                        margin-top: 0;
                        margin-bottom: 10px;
                        font-size: 16px;
                    }
                    
                    .query-code {
                        background-color: #f1f3f5;
                        padding: 10px;
                        border-radius: 4px;
                        overflow-x: auto;
                        font-family: monospace;
                        margin: 0;
                    }
                    
                    .results-info {
                        margin-bottom: 10px;
                        color: #6c757d;
                    }
                    
                    .results-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    
                    .results-table th, .results-table td {
                        padding: 8px 12px;
                        text-align: left;
                        border: 1px solid #dee2e6;
                    }
                    
                    .results-table th {
                        background-color: #e9ecef;
                        font-weight: 600;
                    }
                    
                    .results-table tr:nth-child(even) {
                        background-color: #f8f9fa;
                    }
                    
                    .no-results {
                        padding: 20px;
                        text-align: center;
                        color: #6c757d;
                        background-color: #f8f9fa;
                        border-radius: 4px;
                    }
                `;
                
                document.head.appendChild(style);
            }

            displayResults(results, container, title = '') {
                if (!results || results.length === 0) {
                    container.innerHTML = '<div class="loading">No data to display</div>';
                    return;
                }

                const headers = Object.keys(results[0]);
                
                container.innerHTML = `
                    ${title ? `<h3>${title}</h3>` : ''}
                    <table class="results-table">
                        <thead>
                            <tr>
                                ${headers.map(header => `<th>${header}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${results.map(row => `
                                <tr>
                                    ${headers.map(header => {
                                        const value = row[header];
                                        const displayValue = value !== null && value !== undefined ? 
                                            String(value).substring(0, 100) + (String(value).length > 100 ? '...' : '') : 
                                            '<em>NULL</em>';
                                        return `<td>${displayValue}</td>`;
                                    }).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            }

            handleClear() {
                document.getElementById('query-input').value = '';
                document.getElementById('query-results').innerHTML = '<div class="loading">Execute a query to see results</div>';
                document.getElementById('query-info').innerHTML = '';
            }

            // UI Helper Methods
            showStatus(message, type) {
                const statusText = document.getElementById('status-text');
                const statusDot = document.getElementById('status-dot');
                
                statusText.textContent = message;
                
                // Remove all status classes
                statusDot.classList.remove('connected', 'connecting');
                
                // Add appropriate class
                if (type === 'connected') {
                    statusDot.classList.add('connected');
                } else if (type === 'connecting') {
                    statusDot.classList.add('connecting');
                }
                
                // Update connection info
                if (this.currentConnection) {
                    document.getElementById('connection-info').innerHTML = `
                        <span style="font-size: 0.9rem; color: #888;">
                            ${this.currentConnection.type.toUpperCase()}
                            ${this.selectedDatabase ? ` • ${this.selectedDatabase}` : ''}
                        </span>
                    `;
                }
                
                console.log(`Status: ${message} (${type})`);
            }

            activateStep(stepNumber) {
                // Enable the step
                const stepElement = document.getElementById(`step-${stepNumber}`);
                stepElement.classList.remove('disabled');
                stepElement.classList.add('active');
                
                // Remove active from previous steps
                for (let i = 1; i < stepNumber; i++) {
                    const prevStep = document.getElementById(`step-${i}`);
                    if (prevStep.classList.contains('active')) {
                        prevStep.classList.remove('active');
                    }
                }
                
                this.currentStep = stepNumber;
                console.log(`Activated step ${stepNumber}`);
            }

            completeStep(stepNumber) {
                const stepElement = document.getElementById(`step-${stepNumber}`);
                stepElement.classList.remove('active');
                stepElement.classList.add('completed');
                console.log(`Completed step ${stepNumber}`);
            }

            showPanel(panelId) {
                // Hide all panels
                document.querySelectorAll('.content-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                
                // Show selected panel
                document.getElementById(panelId).classList.add('active');
                console.log(`Showing panel: ${panelId}`);
            }

            // Utility Methods
            async testConnection() {
                try {
                    const response = await fetch(`http://localhost:3002/test`);
                    if (response.ok) {
                        console.log('✅ Server connection test successful');
                        return true;
                    }
                } catch (error) {
                    console.error('❌ Server connection test failed:', error);
                    this.showStatus('Cannot connect to server. Please ensure Node.js server is running on port 3002.', 'error');
                    return false;
                }
            }

            // Export functionality
            exportResults(format) {
                const resultsTable = document.querySelector('#query-results .results-table');
                if (!resultsTable) {
                    this.showStatus('No results to export', 'error');
                    return;
                }

                const headers = Array.from(resultsTable.querySelectorAll('thead th')).map(th => th.textContent);
                const rows = Array.from(resultsTable.querySelectorAll('tbody tr')).map(tr => 
                    Array.from(tr.querySelectorAll('td')).map(td => td.textContent)
                );

                let content, filename, mimeType;

                if (format === 'csv') {
                    content = [
                        headers.join(','),
                        ...rows.map(row => row.map(cell => 
                            cell.includes(',') ? `"${cell.replace(/"/g, '""')}"` : cell
                        ).join(','))
                    ].join('\n');
                    filename = `query_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
                    mimeType = 'text/csv';
                } else if (format === 'json') {
                    const jsonData = rows.map(row => {
                        const obj = {};
                        headers.forEach((header, index) => {
                            obj[header] = row[index];
                        });
                        return obj;
                    });
                    content = JSON.stringify(jsonData, null, 2);
                    filename = `query_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                    mimeType = 'application/json';
                }

                const blob = new Blob([content], { type: mimeType });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showStatus(`Exported results as ${format.toUpperCase()}`, 'connected');
            }

            // Reset application
            reset() {
                this.currentConnection = null;
                this.selectedDatabase = null;
                this.schema = null;
                this.currentStep = 1;

                // Reset UI
                document.getElementById('db-type').value = '';
                document.getElementById('connection-fields').innerHTML = '';
                document.getElementById('database-select').innerHTML = '<option value="">Select database...</option>';
                document.getElementById('query-input').value = '';
                document.getElementById('query-results').innerHTML = '<div class="loading">Execute a query to see results</div>';
                document.getElementById('query-info').innerHTML = '';

                // Reset steps
                for (let i = 1; i <= 4; i++) {
                    const step = document.getElementById(`step-${i}`);
                    step.classList.remove('active', 'completed');
                    if (i > 1) step.classList.add('disabled');
                }
                document.getElementById('step-1').classList.add('active');

                // Show welcome panel
                this.showPanel('welcome-panel');
                this.showStatus('Ready to connect', 'ready');
            }

            async debugConnection() {
                console.log('🔍 Starting connection debug...');
                
                // Test 1: Server reachability
                try {
                    const testResponse = await fetch('http://localhost:3002/test');
                    const testData = await testResponse.json();
                    console.log('✅ Test endpoint works:', testData);
                } catch (error) {
                    console.error('❌ Test endpoint failed:', error);
                    return;
                }
                
                // Test 2: Check if backend is running
                try {
                    const healthResponse = await fetch('http2://localhost:3002/health');
                    const healthData = await healthResponse.json();
                    console.log('✅ Health check works:', healthData);
                } catch (error) {
                    console.error('❌ Health check failed:', error);
                }
                
                // Test 3: Try the actual connection endpoint
                try {
                    const connectResponse = await fetch('http://localhost:3002/api/database/connect', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            type: 'mysql',
                            database: 'test',
                            config: {
                                host: 'localhost',
                                port: 3306,
                                username: 'root',
                                password: 'test'
                            }
                        })
                    });
                    
                    console.log('Connect response status:', connectResponse.status);
                    console.log('Connect response headers:', [...connectResponse.headers.entries()]);
                    
                    const responseText = await connectResponse.text();
                    console.log('Connect response body:', responseText);
                    
                    try {
                        const connectData = JSON.parse(responseText);
                        console.log('✅ Connect endpoint works:', connectData);
                    } catch (parseError) {
                        console.error('❌ Connect endpoint returned non-JSON:', responseText);
                    }
                } catch (error) {
                    console.error('❌ Connect endpoint failed:', error);
                }
            }
        }

        // Initialize the application
        let app;
        document.addEventListener('DOMContentLoaded', () => {
            app = new SQLChatApp();
            
            // Add global functions for buttons
            window.exportCSV = () => app.exportResults('csv');
            window.exportJSON = () => app.exportResults('json');
            window.resetApp = () => app.reset();
            
            console.log('SQL Chat App loaded successfully');
        });

        // Parallax effect for background
        document.addEventListener('mousemove', (e) => {
            const x = (e.clientX / window.innerWidth - 0.5) * 40;
            const y = (e.clientY / window.innerHeight - 0.5) * 40;
            document.querySelector('.parallax-bg').style.backgroundPosition = `${50 + x}% ${50 + y}%`;
        });
    </script>
</body>
</html>
