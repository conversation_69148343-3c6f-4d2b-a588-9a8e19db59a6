# <UPDATED_CODE>
import re
import hashlib
import time
import threading
from collections import defaultdict, OrderedDict
from concurrent.futures import ThreadPoolExecutor, as_completed
from contextlib import contextmanager
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from datetime import datetime, timezone, timedelta
import json
import uuid
import traceback
import logging
import os
import sys
from functools import wraps, lru_cache
from threading import Lock, RLock
import queue
import weakref

from flask import Flask, request, jsonify
from flask_cors import CORS
import pymongo
import mysql.connector
import sqlite3
import psycopg2
from groq import Groq
from dotenv import load_dotenv

# Initialize Flask app and enable CORS
app = Flask(__name__)
CORS(app)

# Enhanced logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.FileHandler('sql_chat_backend.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Security and Performance Constants
MAX_QUERY_LENGTH = 10000
MAX_RESULTS_LIMIT = 1000
DEFAULT_RESULTS_LIMIT = 100
CONNECTION_TIMEOUT = 30
QUERY_TIMEOUT = 60
MAX_CONNECTIONS_PER_SESSION = 5
RATE_LIMIT_WINDOW = 60  # seconds
RATE_LIMIT_REQUESTS = 100
CACHE_TTL = 300  # 5 minutes
MAX_CACHE_SIZE = 1000

# SQL Injection Prevention Patterns
DANGEROUS_SQL_PATTERNS = [
    r';\s*(drop|delete|update|insert|create|alter|truncate)\s+',
    r'union\s+select',
    r'--\s*',
    r'/\*.*?\*/',
    r'xp_cmdshell',
    r'sp_executesql',
    r'exec\s*\(',
    r'execute\s*\(',
]

@dataclass
class ErrorContext:
    """Enhanced error context for better debugging"""
    error_type: str
    message: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    session_id: Optional[str] = None
    query: Optional[str] = None
    database: Optional[str] = None
    table: Optional[str] = None
    stack_trace: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'error_type': self.error_type,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'session_id': self.session_id,
            'query': self.query[:100] + '...' if self.query and len(self.query) > 100 else self.query,
            'database': self.database,
            'table': self.table,
            'stack_trace': self.stack_trace
        }

class SecurityValidator:
    """Enhanced security validation for SQL queries and inputs"""
    
    @staticmethod
    def validate_sql_query(query: str) -> Tuple[bool, str]:
        """Validate SQL query for potential security threats"""
        if not query or len(query.strip()) == 0:
            return False, "Empty query not allowed"
        
        if len(query) > MAX_QUERY_LENGTH:
            return False, f"Query too long. Maximum length is {MAX_QUERY_LENGTH} characters"
        
        query_lower = query.lower()
        
        # Check for dangerous patterns
        for pattern in DANGEROUS_SQL_PATTERNS:
            if re.search(pattern, query_lower, re.IGNORECASE):
                return False, f"Potentially dangerous SQL pattern detected: {pattern}"
        
        # Check for multiple statements (basic check)
        statements = [s.strip() for s in query.split(';') if s.strip()]
        if len(statements) > 1:
            return False, "Multiple SQL statements not allowed"
        
        # Must start with SELECT for read-only operations
        if not query_lower.strip().startswith('select'):
            return False, "Only SELECT queries are allowed"
        
        return True, "Query validation passed"
    
    @staticmethod
    def sanitize_identifier(identifier: str) -> str:
        """Sanitize database identifiers (table names, column names)"""
        if not identifier:
            raise ValueError("Identifier cannot be empty")
        
        # Remove dangerous characters
        sanitized = re.sub(r'[^\w\-_]', '', identifier)
        
        if not sanitized:
            raise ValueError("Invalid identifier after sanitization")
        
        if len(sanitized) > 64:  # MySQL limit
            raise ValueError("Identifier too long")
        
        return sanitized
    
    @staticmethod
    def validate_connection_config(db_type: str, config: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate database connection configuration"""
        required_fields = {
            'mysql': ['host', 'username', 'password'],
            'postgresql': ['host', 'username', 'password'],
            'mongodb': ['host'],
            'sqlite': ['database_path']
        }
        
        if db_type not in required_fields:
            return False, f"Unsupported database type: {db_type}"
        
        for field in required_fields[db_type]:
            if field not in config or not config[field]:
                return False, f"Missing required field: {field}"
        
        # Validate host format
        if 'host' in config:
            host = config['host']
            if not re.match(r'^[a-zA-Z0-9\-\.]+$', host):  # Fixed the regex pattern
                return False, "Invalid host format"
        
        # Validate port
        if 'port' in config:
            try:
                port = int(config['port'])
                if port < 1 or port > 65535:
                    return False, "Port must be between 1 and 65535"
            except (ValueError, TypeError):
                return False, "Invalid port format"
        
        return True, "Configuration validation passed"

class RateLimiter:
    """Rate limiting implementation using token bucket algorithm"""
    
    def __init__(self):
        self.clients = defaultdict(lambda: {'tokens': RATE_LIMIT_REQUESTS, 'last_update': time.time()})
        self.lock = Lock()
    
    def is_allowed(self, client_id: str) -> bool:
        """Check if client is allowed to make a request"""
        with self.lock:
            now = time.time()
            client = self.clients[client_id]
            
            # Refill tokens based on time passed
            time_passed = now - client['last_update']
            tokens_to_add = time_passed * (RATE_LIMIT_REQUESTS / RATE_LIMIT_WINDOW)
            client['tokens'] = min(RATE_LIMIT_REQUESTS, client['tokens'] + tokens_to_add)
            client['last_update'] = now
            
            if client['tokens'] >= 1:
                client['tokens'] -= 1
                return True
            
            return False

class LRUCache:
    """Thread-safe LRU Cache implementation"""
    
    def __init__(self, max_size: int = MAX_CACHE_SIZE, ttl: int = CACHE_TTL):
        self.max_size = max_size
        self.ttl = ttl
        self.cache = OrderedDict()
        self.lock = RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self.lock:
            if key not in self.cache:
                return None
            
            value, timestamp = self.cache[key]
            
            # Check if expired
            if time.time() - timestamp > self.ttl:
                del self.cache[key]
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            return value
    
    def set(self, key: str, value: Any) -> None:
        """Set item in cache"""
        with self.lock:
            if key in self.cache:
                self.cache.move_to_end(key)
            elif len(self.cache) >= self.max_size:
                # Remove least recently used
                self.cache.popitem(last=False)
            
            self.cache[key] = (value, time.time())
    
    def clear(self) -> None:
        """Clear all cache"""
        with self.lock:
            self.cache.clear()

class ConnectionPool:
    """Database connection pool for better resource management"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.pools = {}
        self.lock = Lock()
    
    def get_pool_key(self, db_type: str, config: Dict[str, Any]) -> str:
        """Generate unique key for connection pool"""
        config_str = json.dumps(config, sort_keys=True)
        return hashlib.md5(f"{db_type}:{config_str}".encode()).hexdigest()
    
    @contextmanager
    def get_connection(self, db_type: str, config: Dict[str, Any]):
        """Get connection from pool with context manager"""
        pool_key = self.get_pool_key(db_type, config)
        
        with self.lock:
            if pool_key not in self.pools:
                self.pools[pool_key] = queue.Queue(maxsize=self.max_connections)
        
        pool = self.pools[pool_key]
        connection = None
        
        try:
            # Try to get existing connection
            try:
                connection = pool.get_nowait()
                # Test connection
                if not self._test_connection(connection, db_type):
                    connection = None
            except queue.Empty:
                pass
            
            # Create new connection if needed
            if connection is None:
                connection = self._create_connection(db_type, config)
            
            yield connection
            
        except Exception as e:
            logger.error(f"Connection error: {e}")
            if connection:
                try:
                    connection.close()
                except:
                    pass
            raise
        finally:
            # Return connection to pool
            if connection:
                try:
                    if pool.qsize() < self.max_connections:
                        pool.put_nowait(connection)
                    else:
                        connection.close()
                except:
                    try:
                        connection.close()
                    except:
                        pass
    
    def _create_connection(self, db_type: str, config: Dict[str, Any]):
        """Create new database connection"""
        if db_type == 'mysql':
            return mysql.connector.connect(
                host=config['host'],
                user=config['username'],
                password=config['password'],
                port=config.get('port', 3306),
                charset='utf8mb4',
                autocommit=True,
                connection_timeout=CONNECTION_TIMEOUT,
                buffered=True  # Add this to prevent unread results
            )
        elif db_type == 'postgresql':
            return psycopg2.connect(
                host=config['host'],
                user=config['username'],
                password=config['password'],
                port=config.get('port', 5432),
                connect_timeout=CONNECTION_TIMEOUT
            )
        elif db_type == 'sqlite':
            return sqlite3.connect(
                config['database_path'],
                check_same_thread=False,
                timeout=CONNECTION_TIMEOUT
            )
        elif db_type == 'mongodb':
            # Add MongoDB support
            host = config.get('host', 'localhost')
            port = config.get('port', 27017)
            username = config.get('username')
            password = config.get('password')
        
            if username and password:
                uri = f"mongodb://{username}:{password}@{host}:{port}/"
            else:
                uri = f"mongodb://{host}:{port}/"
        
            return pymongo.MongoClient(
                uri,
                serverSelectionTimeoutMS=CONNECTION_TIMEOUT * 1000,
                connectTimeoutMS=CONNECTION_TIMEOUT * 1000,
                socketTimeoutMS=CONNECTION_TIMEOUT * 1000
            )
        else:
            raise ValueError(f"Unsupported database type: {db_type}")
    
    def _test_connection(self, connection, db_type: str) -> bool:
        """Test if connection is still valid"""
        try:
            if db_type == 'mysql':
                # For MySQL, use ping which doesn't leave unread results
                connection.ping(reconnect=False)
            elif db_type == 'postgresql':
                cursor = connection.cursor()
                cursor.execute('SELECT 1')
                cursor.fetchall()  # Consume all results
                cursor.close()
            elif db_type == 'sqlite':
                cursor = connection.cursor()
                cursor.execute('SELECT 1')
                cursor.fetchall()  # Consume all results
                cursor.close()
            elif db_type == 'mongodb':
                # For MongoDB, test with admin command
                connection.admin.command('ping')
            return True
        except Exception as e:
            logger.debug(f"Connection test failed for {db_type}: {e}")
            return False

class EnhancedConnectionManager:
    """Enhanced connection manager with better tracking and cleanup"""
    
    def __init__(self):
        self.active_connections = {}
        self.connection_metadata = {}
        self.selected_tables = {}
        self.session_locks = defaultdict(RLock)
        self.connection_pool = ConnectionPool()
        self.cache = LRUCache()
        self.cleanup_thread = threading.Thread(target=self._cleanup_expired_sessions, daemon=True)
        self.cleanup_thread.start()
    
    def store_connection(self, session_id: str, db_type: str, database: str, 
                        connection_config: Dict[str, Any], metadata: Dict[str, Any]):
        """Store connection metadata with thread safety"""
        with self.session_locks[session_id]:
            self.active_connections[session_id] = {
                'type': db_type,
                'database': database,
                'config': connection_config,
                'connected_at': datetime.now(timezone.utc),
                'last_activity': datetime.now(timezone.utc)
            }
            self.connection_metadata[session_id] = {
                **metadata,
                'connected_at': datetime.now(timezone.utc),
                'last_activity': datetime.now(timezone.utc),
                'database': database,
                'type': db_type,
                'config': connection_config
            }
            logger.info(f"Connection stored for session {session_id}: {db_type}/{database}")
    
    def update_activity(self, session_id: str):
        """Update last activity timestamp"""
        with self.session_locks[session_id]:
            if session_id in self.active_connections:
                self.active_connections[session_id]['last_activity'] = datetime.now(timezone.utc)
            if session_id in self.connection_metadata:
                self.connection_metadata[session_id]['last_activity'] = datetime.now(timezone.utc)
    
    def get_connection(self, session_id: str, db_type: str, config: Dict[str, Any]):
        """Get connection using connection pool"""
        self.update_activity(session_id)
        return self.connection_pool.get_connection(db_type, config)
    
    def _cleanup_expired_sessions(self):
        """Background thread to cleanup expired sessions"""
        while True:
            try:
                time.sleep(300)  # Check every 5 minutes
                current_time = datetime.now(timezone.utc)
                expired_sessions = []
                
                for session_id, conn_info in self.active_connections.items():
                    last_activity = conn_info.get('last_activity', conn_info['connected_at'])
                    if current_time - last_activity > timedelta(hours=1):  # 1 hour timeout
                        expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    logger.info(f"Cleaning up expired session: {session_id}")
                    self.remove_connection(session_id)
                    
            except Exception as e:
                logger.error(f"Error in cleanup thread: {e}")
    
    def set_selected_table(self, session_id: str, table_name: str):
        """Set selected table with validation"""
        with self.session_locks[session_id]:
            sanitized_table = SecurityValidator.sanitize_identifier(table_name)
            self.selected_tables[session_id] = sanitized_table
            self.update_activity(session_id)
            logger.info(f"Selected table for session {session_id}: {sanitized_table}")
    
    def get_selected_table(self, session_id: str) -> Optional[str]:
        """Get selected table"""
        with self.session_locks[session_id]:
            self.update_activity(session_id)
            return self.selected_tables.get(session_id)
    
    def get_connection_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get connection info with activity update"""
        with self.session_locks[session_id]:
            info = self.connection_metadata.get(session_id)
            if info:
                self.update_activity(session_id)
                info['selected_table'] = self.get_selected_table(session_id)
                # Convert datetime objects to ISO format
                for key in ['connected_at', 'last_activity']:
                    if key in info and isinstance(info[key], datetime):
                        info[key] = info[key].isoformat()
            return info
    
    def remove_connection(self, session_id: str):
        """Remove connection with proper cleanup"""
        with self.session_locks[session_id]:
            self.active_connections.pop(session_id, None)
            self.connection_metadata.pop(session_id, None)
            self.selected_tables.pop(session_id, None)
            logger.info(f"Connection removed for session {session_id}")

    def update_selected_database(self, session_id: str, database: str):
        """Update selected database for session"""
        with self.session_locks[session_id]:
            if session_id in self.connection_metadata:
                self.connection_metadata[session_id]['database'] = database
                self.update_activity(session_id)
                logger.info(f"Updated selected database for session {session_id}: {database}")

class EnhancedSQLDatabaseManager:
    """Enhanced SQL database manager with connection pooling and caching"""
    
    def __init__(self, connection_manager: EnhancedConnectionManager):
        self.connection_manager = connection_manager
        self.executor = ThreadPoolExecutor(max_workers=10)
    
    def connect(self, session_id: str, db_type: str, connection_config: Dict[str, Any]) -> Dict[str, Any]:
        """Test database connection with enhanced error handling"""
        try:
            # Validate configuration
            is_valid, validation_message = SecurityValidator.validate_connection_config(db_type, connection_config)
            if not is_valid:
                return {"success": False, "message": f"Configuration error: {validation_message}"}
            
            # Test connection
            with self.connection_manager.get_connection(session_id, db_type, connection_config) as conn:
                # Test with a simple query
                if db_type == 'mysql':
                    cursor = conn.cursor(buffered=True)  # Use buffered cursor
                    cursor.execute("SELECT 1")
                    cursor.fetchall()  # Consume all results
                    cursor.close()
                elif db_type == 'postgresql':
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchall()  # Consume all results
                    cursor.close()
                elif db_type == 'sqlite':
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchall()  # Consume all results
                    cursor.close()
                elif db_type == 'mongodb':
                    conn.admin.command('ping')
        
            logger.info(f"Successfully connected to {db_type} database for session {session_id}")
            return {"success": True, "message": f"{db_type.upper()} connection successful"}
            
        except Exception as e:
            error_context = ErrorContext(
                error_type="ConnectionError",
                message=str(e),
                session_id=session_id
            )
            logger.error(f"Connection failed: {error_context.to_dict()}")
            return {"success": False, "message": str(e)}

    def get_databases(self, session_id: str) -> List[str]:
        """Get list of databases"""
        try:
            connection_info = self.connection_manager.get_connection_info(session_id)
            if not connection_info:
                raise ValueError("No active connection found")
        
            db_type = connection_info['type']
            config = connection_info['config']
        
            with self.connection_manager.get_connection(session_id, db_type, config) as conn:
                if db_type == 'mysql':
                    cursor = conn.cursor(buffered=True)
                    cursor.execute("SHOW DATABASES")
                    databases = [row[0] for row in cursor.fetchall()]
                    cursor.close()
                    # Filter out system databases
                    return [db for db in databases if db not in ['information_schema', 'performance_schema', 'mysql', 'sys']]
            
                elif db_type == 'postgresql':
                    cursor = conn.cursor()
                    cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
                    databases = [row[0] for row in cursor.fetchall()]
                    cursor.close()
                    return [db for db in databases if db not in ['postgres', 'template0', 'template1']]
            
                elif db_type == 'sqlite':
                    # SQLite has only one database per file
                    return ['main']
            
                else:
                    raise ValueError(f"Unsupported database type: {db_type}")
                
        except Exception as e:
            logger.error(f"Failed to get databases: {e}")
            raise

    def get_tables(self, session_id: str, database: str) -> List[str]:
        """Get list of tables in database"""
        try:
            connection_info = self.connection_manager.get_connection_info(session_id)
            if not connection_info:
                raise ValueError("No active connection found")
        
            db_type = connection_info['type']
            config = connection_info['config']
        
            with self.connection_manager.get_connection(session_id, db_type, config) as conn:
                if db_type == 'mysql':
                    cursor = conn.cursor(buffered=True)
                    cursor.execute(f"USE `{database}`")
                    cursor.execute("SHOW TABLES")
                    tables = [row[0] for row in cursor.fetchall()]
                    cursor.close()
                    return tables
            
                elif db_type == 'postgresql':
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_catalog = '{database}'")
                    tables = [row[0] for row in cursor.fetchall()]
                    cursor.close()
                    return tables
            
                elif db_type == 'sqlite':
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    cursor.close()
                    return tables
            
                else:
                    raise ValueError(f"Unsupported database type: {db_type}")
                
        except Exception as e:
            logger.error(f"Failed to get tables: {e}")
            raise

    def get_schema_info(self, session_id: str, database: str) -> Dict[str, Any]:
        """Get detailed schema information for a database"""
        try:
            connection_info = self.connection_manager.get_connection_info(session_id)
            if not connection_info:
                raise ValueError("No active connection found")
            
            db_type = connection_info.get('type')
            config = connection_info.get('config', {})
            
            schema = {}
            
            if db_type == 'mysql':
                # Create a new connection with the specific database
                config_copy = config.copy()
                config_copy['database'] = database
                conn = mysql.connector.connect(**config_copy)
                
                cursor = conn.cursor(dictionary=True)
                
                # Get tables
                cursor.execute("SHOW TABLES")
                tables = [row[f'Tables_in_{database}'] for row in cursor.fetchall()]
                
                # Get detailed schema for each table
                for table in tables:
                    # Get columns
                    cursor.execute(f"DESCRIBE `{table}`")
                    columns_info = cursor.fetchall()
                    
                    # Get table information
                    cursor.execute(f"SHOW TABLE STATUS LIKE '{table}'")
                    table_info = cursor.fetchone()
                    
                    schema[table] = {
                        'columns': [col['Field'] for col in columns_info],
                        'columnInfo': {
                            col['Field']: {
                                'type': col['Type'],
                                'nullable': col['Null'] == 'YES',
                                'key': col['Key'],
                                'default': col['Default'],
                                'extra': col['Extra']
                            } for col in columns_info
                        },
                        'rowCount': table_info['Rows'] if table_info else 0,
                        'description': table_info['Comment'] if table_info and table_info['Comment'] else f"Table: {table}"
                    }
                
                cursor.close()
                conn.close()
                
            elif db_type == 'postgresql':
                # PostgreSQL implementation
                config_copy = config.copy()
                config_copy['database'] = database
                conn = psycopg2.connect(**config_copy)

                cursor = conn.cursor()

                # Get tables
                cursor.execute("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
                """)
                tables = [row[0] for row in cursor.fetchall()]

                # Get detailed schema for each table
                for table in tables:
                    # Get columns
                    cursor.execute("""
                        SELECT column_name, data_type, is_nullable, column_default,
                               character_maximum_length, numeric_precision, numeric_scale
                        FROM information_schema.columns
                        WHERE table_name = %s AND table_schema = 'public'
                        ORDER BY ordinal_position
                    """, (table,))
                    columns_info = cursor.fetchall()

                    # Get row count
                    cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                    row_count = cursor.fetchone()[0]

                    schema[table] = {
                        'columns': [col[0] for col in columns_info],
                        'columnInfo': {
                            col[0]: {
                                'type': col[1],
                                'nullable': col[2] == 'YES',
                                'default': col[3],
                                'maxLength': col[4],
                                'precision': col[5],
                                'scale': col[6]
                            } for col in columns_info
                        },
                        'rowCount': row_count,
                        'description': f"Table: {table}"
                    }

                cursor.close()
                conn.close()

            elif db_type == 'sqlite':
                # SQLite implementation
                conn = sqlite3.connect(config.get('database', database))
                cursor = conn.cursor()

                # Get tables
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                # Get detailed schema for each table
                for table in tables:
                    # Get columns
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns_info = cursor.fetchall()

                    # Get row count
                    cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                    row_count = cursor.fetchone()[0]

                    schema[table] = {
                        'columns': [col[1] for col in columns_info],
                        'columnInfo': {
                            col[1]: {
                                'type': col[2],
                                'nullable': not col[3],  # NOT NULL flag
                                'default': col[4],
                                'primaryKey': col[5] == 1
                            } for col in columns_info
                        },
                        'rowCount': row_count,
                        'description': f"Table: {table}"
                    }

                cursor.close()
                conn.close()

            elif db_type == 'mongodb':
                # MongoDB implementation
                client = pymongo.MongoClient(config.get('uri', 'mongodb://localhost:27017/'))
                db = client[database]

                # Get collections
                collections = db.list_collection_names()

                # Get detailed schema for each collection
                for collection_name in collections:
                    collection = db[collection_name]

                    # Get sample document to infer schema
                    sample_doc = collection.find_one()
                    columns = list(sample_doc.keys()) if sample_doc else []

                    # Get document count
                    doc_count = collection.count_documents({})

                    schema[collection_name] = {
                        'columns': columns,
                        'columnInfo': {
                            col: {
                                'type': type(sample_doc.get(col)).__name__ if sample_doc else 'unknown',
                                'nullable': True  # MongoDB fields are generally nullable
                            } for col in columns
                        } if sample_doc else {},
                        'rowCount': doc_count,
                        'description': f"Collection: {collection_name}"
                    }

                client.close()
                
            return schema
            
        except Exception as e:
            logger.error(f"Failed to get schema info: {e}")
            raise

    def get_table_data(self, session_id: str, database: str, table: str, limit: int = 100) -> List[Dict]:
        """Get data from table"""
        try:
            connection_info = self.connection_manager.get_connection_info(session_id)
            if not connection_info:
                raise ValueError("No active connection found")
        
            db_type = connection_info['type']
            config = connection_info['config']
        
            # Sanitize inputs
            table = SecurityValidator.sanitize_identifier(table)
            limit = min(limit, MAX_RESULTS_LIMIT)
        
            with self.connection_manager.get_connection(session_id, db_type, config) as conn:
                if db_type == 'mysql':
                    cursor = conn.cursor(buffered=True, dictionary=True)
                    cursor.execute(f"USE `{database}`")
                    cursor.execute(f"SELECT * FROM `{table}` LIMIT {limit}")
                    results = cursor.fetchall()
                    cursor.close()
                    return results
            
                elif db_type == 'postgresql':
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT * FROM \"{table}\" LIMIT {limit}")
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    cursor.close()
                    return [dict(zip(columns, row)) for row in rows]
            
                elif db_type == 'sqlite':
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT * FROM `{table}` LIMIT {limit}")
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    cursor.close()
                    return [dict(zip(columns, row)) for row in rows]
            
                else:
                    raise ValueError(f"Unsupported database type: {db_type}")
                
        except Exception as e:
            logger.error(f"Failed to get table data: {e}")
            raise

    def execute_query(self, session_id: str, database: str, query: str) -> List[Dict]:
        """Execute SQL query"""
        try:
            # Validate query
            is_valid, validation_message = SecurityValidator.validate_sql_query(query)
            if not is_valid:
                raise ValueError(f"Query validation failed: {validation_message}")
        
            connection_info = self.connection_manager.get_connection_info(session_id)
            if not connection_info:
                raise ValueError("No active connection found")
        
            db_type = connection_info['type']
            config = connection_info['config']
        
            with self.connection_manager.get_connection(session_id, db_type, config) as conn:
                if db_type == 'mysql':
                    cursor = conn.cursor(buffered=True, dictionary=True)
                    cursor.execute(f"USE `{database}`")
                    cursor.execute(query)
                    results = cursor.fetchall()
                    cursor.close()
                    return results
            
                elif db_type == 'postgresql':
                    cursor = conn.cursor()
                    cursor.execute(query)
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    cursor.close()
                    return [dict(zip(columns, row)) for row in rows]
            
                elif db_type == 'sqlite':
                    cursor = conn.cursor()
                    cursor.execute(query)
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    cursor.close()
                    return [dict(zip(columns, row)) for row in rows]
            
                else:
                    raise ValueError(f"Unsupported database type: {db_type}")
                
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise

@app.route('/api/connect', methods=['POST'])
def connect_to_database():
    """Connect to database with credentials"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400
            
        db_type = data.get('type')
        connection_config = data.get('config', {})
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not db_type:
            return jsonify({"success": False, "message": "Database type required"}), 400
        
        if not connection_config:
            return jsonify({"success": False, "message": "Connection config required"}), 400
    
        logger.info(f"Attempting to connect to {db_type} database for session {session_id}")
    
        # Use the enhanced SQL manager for connection
        result = sql_manager.connect(session_id, db_type, connection_config)
    
        if result['success']:
            # Store connection metadata
            connection_manager.store_connection(
                session_id=session_id,
                db_type=db_type,
                database="",  # Will be selected later
                connection_config=connection_config,
                metadata={
                    'connected_at': datetime.now(timezone.utc).isoformat(),
                    'host': connection_config.get('host', 'localhost'),
                    'port': connection_config.get('port'),
                    'username': connection_config.get('username')
                }
            )
        
            return jsonify({
                "success": True,
                "session_id": session_id,
                "message": result['message']
            })
        else:
            return jsonify(result), 400
        
    except Exception as e:
        logger.error(f"Connection error: {e}")
        return jsonify({
            "success": False,
            "message": str(e)
        }), 500

@app.route('/api/databases', methods=['GET'])
def list_databases():
    session_id = request.args.get('session_id')
    if not session_id:
        return jsonify({'success': False, 'message': 'Session ID required'}), 400
    try:
        databases = sql_manager.get_databases(session_id)
        return jsonify({'success': True, 'databases': [{'name': db, 'type': 'mysql'} for db in databases]})
    except Exception as e:
        logger.error(f"Error fetching databases: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/select-database', methods=['POST'])
def select_database():
    data = request.get_json()
    session_id = data.get('session_id')
    database = data.get('database')
    if not session_id or not database:
        return jsonify({'success': False, 'message': 'session_id and database are required'}), 400
    try:
        connection_manager.update_selected_database(session_id, database)
        return jsonify({'success': True, 'message': f'Database {database} selected for session {session_id}'})
    except Exception as e:
        logger.error(f"Failed to select database: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tables', methods=['GET'])
def list_tables():
    session_id = request.args.get('session_id')
    database = request.args.get('database')
    if not session_id or not database:
        return jsonify({'success': False, 'message': 'Session ID and database required'}), 400
    try:
        tables = sql_manager.get_tables(session_id, database)
        return jsonify({'success': True, 'tables': [{'name': tbl} for tbl in tables]})
    except Exception as e:
        logger.error(f"Error fetching tables: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/schema', methods=['GET'])
def get_schema():
    session_id = request.args.get('session_id')
    database = request.args.get('database')
    if not session_id or not database:
        return jsonify({'success': False, 'message': 'Session ID and database required'}), 400
    try:
        schema = sql_manager.get_schema_info(session_id, database)
        return jsonify({'success': True, 'schema': schema})
    except Exception as e:
        logger.error(f"Error fetching schema: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/query', methods=['POST'])
def execute_sql():
    data = request.get_json()
    session_id = data.get('session_id')
    database = data.get('database')
    query = data.get('query')
    if not all([session_id, database, query]):
        return jsonify({'success': False, 'message': 'session_id, database and query are required'}), 400
    try:
        results = sql_manager.execute_query(session_id, database, query)
        return jsonify({'success': True, 'results': results, 'count': len(results)})
    except Exception as e:
        logger.error(f"Error executing query: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/table-data', methods=['GET'])
def get_table_data():
    session_id = request.args.get('session_id')
    table = request.args.get('table')
    limit = request.args.get('limit', 100, type=int)
    if not session_id or not table:
        return jsonify({'success': False, 'message': 'session_id and table are required'}), 400
    try:
        connection_info = connection_manager.get_connection_info(session_id)
        if not connection_info:
            return jsonify({'success': False, 'message': 'No active connection found'}), 400
        database = connection_info.get('database')
        if not database:
            return jsonify({'success': False, 'message': 'No database selected for session'}), 400
        data = sql_manager.get_table_data(session_id, database, table, limit)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        logger.error(f"Failed to get table data: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/ai-query', methods=['POST'])
def ai_query():
    data = request.get_json()
    session_id = data.get('session_id')
    database = data.get('database')
    question = data.get('question')
    db_type = data.get('db_type')
    selected_table = data.get('table')  # Get the selected table
    
    if not all([session_id, database, question]):
        return jsonify({"success": False, "error": "Session ID, database, and question required"}), 400
    
    try:
        # If db_type is not provided, get it from the connection info
        if not db_type:
            connection_info = connection_manager.get_connection_info(session_id)
            if connection_info and 'type' in connection_info:
                db_type = connection_info['type']
            else:
                db_type = 'mysql'
                logger.warning(f"Database type not provided, defaulting to {db_type}")
        
        # Get schema information
        if db_type == 'mongodb':
            schema = mongodb_manager.get_schema_info(session_id, database)
            generated_query = ai_generator.generate_mongodb_query(question, schema, selected_table)
        else:
            schema = sql_manager.get_schema_info(session_id, database)
            generated_query = ai_generator.generate_sql_query(question, schema, db_type, selected_table)
        
        # Prepare response
        response_data = {
            "success": True,
            "generated_query": generated_query,
            "question": question,
            "database_type": db_type,
            "selected_table": selected_table
        }
        
        # Execute the query if requested
        execute_query = data.get('execute', True)
        if execute_query:
            try:
                if db_type == 'mongodb':
                    results = mongodb_manager.execute_query(session_id, database, generated_query)
                else:
                    results = sql_manager.execute_query(session_id, database, generated_query)
                
                response_data.update({
                    "results": results,
                    "count": len(results)
                })
            except Exception as exec_error:
                logger.error(f"Query execution failed: {exec_error}")
                response_data.update({
                    "execution_error": str(exec_error)
                })
        
        return jsonify(response_data)
    except Exception as e:
        logger.error(f"AI query error: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/connection-info', methods=['GET'])
def get_connection_info():
    """Get connection information for a session"""
    session_id = request.args.get('session_id')
    if not session_id:
        return jsonify({'success': False, 'message': 'Session ID required'}), 400
    
    try:
        connection_info = connection_manager.get_connection_info(session_id)
        if not connection_info:
            return jsonify({'success': False, 'message': 'No active connection found'}), 404
        
        # Return connection info with sensitive data removed
        safe_info = {
            'session_id': session_id,
            'type': connection_info.get('type', 'unknown'),
            'database': connection_info.get('database', ''),
            'connected_at': connection_info.get('metadata', {}).get('connected_at', ''),
            'host': connection_info.get('config', {}).get('host', 'localhost'),
            'port': connection_info.get('config', {}).get('port', 0)
        }
        
        return jsonify({
            'success': True,
            'connection_info': safe_info
        })
        
    except Exception as e:
        logger.error(f"Failed to get connection info: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# Initialize managers
connection_manager = EnhancedConnectionManager()
sql_manager = EnhancedSQLDatabaseManager(connection_manager)

# Create a simple placeholder for MongoDB (since you're using MySQL)
class MongoDBManager:
    def connect(self, session_id, config):
        return {"success": False, "message": "MongoDB not implemented"}

mongodb_manager = MongoDBManager()

# Create a simple placeholder for AI (since you're testing connection)
class AIQueryGenerator:
    def generate_sql_query(self, *args):
        return "SELECT 1"

ai_generator = AIQueryGenerator()

if __name__ == '__main__':
    try:
        print("🚀 Starting SQL Chat Backend Server...")
        print("📡 Server will run on http://localhost:5001")
        print("🔗 CORS enabled for frontend connections")
        print("=" * 50)
        
        # Test database drivers
        try:
            import mysql.connector
            print("✅ MySQL driver available")
        except ImportError:
            print("❌ MySQL driver not available")
        
        try:
            import psycopg2
            print("✅ PostgreSQL driver available")
        except ImportError:
            print("❌ PostgreSQL driver not available")
        
        try:
            import pymongo
            print("✅ MongoDB driver available")
        except ImportError:
            print("❌ MongoDB driver not available")
        
        print("=" * 50)
        
        app.run(host='0.0.0.0', port=5001, debug=True, threaded=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
