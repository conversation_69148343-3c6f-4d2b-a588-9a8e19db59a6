/**@
   * SQL Chat Bot Node.js Middleware - ENHANCED VERSION
   * Handles communication between frontend and Python backend with added security and resilience features.
   */

const express = require('express');
const axios = require('axios');
const cors = require('cors');
const path = require('path');
const { performance } = require('perf_hooks');
const rateLimit = require('express-rate-limit');
const Joi = require('joi');
const rax = require('retry-axios');

// Configure Axios with retry logic
axios.defaults.raxConfig = {
    instance: axios,
    retry: 3,
    noResponseRetries: 3,
    retryDelay: 100,
    httpMethodsToRetry: ['GET', 'POST'],
    statusCodesToRetry: [[500, 599]]
};

class QueryCache {
    constructor(maxSize = 100, ttl = 300000) { // 5 minutes TTL
      this.cache = new Map();
      this.maxSize = maxSize;
      this.ttl = ttl;
      this.hitCount = 0;
      this.missCount = 0;
    }

    generateKey(query, database, type) {
      return `${type}:${database}:${query.toLowerCase().trim()}`;
    }

    get(query, database, type) {
      const key = this.generateKey(query, database, type);
      const item = this.cache.get(key);
      if (!item || Date.now() - item.timestamp > this.ttl) {
        this.missCount++;
        if (item) this.cache.delete(key); // Clean expired items
        return null;
      }
      this.hitCount++;
      return item.data;
    }

    set(query, database, data, type) {
      if (this.cache.size >= this.maxSize) {
        const firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
      }
      const key = this.generateKey(query, database, type);
      this.cache.set(key, { data, timestamp: Date.now() });
    }

    clear() {
      this.cache.clear();
      this.hitCount = 0;
      this.missCount = 0;
    }

    getStats() {
      const totalRequests = this.hitCount + this.missCount;
      return {
        size: this.cache.size,
        maxSize: this.maxSize,
        hitRate: totalRequests > 0 ? (this.hitCount / totalRequests * 100) : 0,
        missRate: totalRequests > 0 ? (this.missCount / totalRequests * 100) : 0,
        totalRequests: totalRequests
      };
    }
}

class RequestLogger {
    constructor() {
      this.requests = [];
      this.maxLogs = 1000;
      this.stats = {
        totalQueries: 0,
        successfulQueries: 0,
        totalDuration: 0,
        recentHourRequests: 0
      };
    }

    log(req, res, duration) {
      const logEntry = {
        timestamp: new Date().toISOString(),
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        duration: Math.round(duration),
        status: res.statusCode,
        query: req.body?.query?.substring(0, 100) || '',
        database: req.body?.database || req.query?.database || '',
        type: req.body?.type || req.query?.type || ''
      };
      this.requests.unshift(logEntry);
      if (this.requests.length > this.maxLogs) {
        this.requests.pop();
      }
      this.updateStats({
        type: req.path.includes('/query') ? 'query' : 'other',
        success: res.statusCode < 400,
        duration: Math.round(duration)
      });
    }

    updateStats(data) {
      if (data.type === 'query') {
        this.stats.totalQueries++;
        if (data.success) {
          this.stats.successfulQueries++;
        }
        this.stats.totalDuration += data.duration;
      }
    }

    getRecentLogs(limit = 50) {
      return this.requests.slice(0, limit);
    }

    getStats() {
      const oneHourAgo = Date.now() - 3600000;
      this.stats.recentHourRequests = this.requests.filter(
        req => new Date(req.timestamp) > oneHourAgo
      ).length;
      return {
        totalQueries: this.stats.totalQueries,
        successRate: this.stats.totalQueries ? 
          (this.stats.successfulQueries / this.stats.totalQueries * 100) : 0,
        avgResponseTime: this.stats.totalQueries ? 
          (this.stats.totalDuration / this.stats.totalQueries) : 0,
        recentHourRequests: this.stats.recentHourRequests,
        totalRequests: this.requests.length
      };
    }
}

class SQLChatMiddleware {
    constructor() {
      this.app = express();
      // Change port from 5000 to 5001 to match Python backend
      this.backendUrl = process.env.BACKEND_URL || 'http://localhost:5001';
      this.cache = new QueryCache();
      this.logger = new RequestLogger();
      this.setupMiddleware();
      this.setupRoutes();
      this.setupErrorHandling();
    }

    setupMiddleware() {
      this.app.set('trust proxy', 1); // Set to 1 to avoid permissive trust proxy error
    
      // CORS configuration
      this.app.use(cors({
        origin: function(origin, callback) {
          if (!origin) return callback(null, true);
          const allowedOrigins = [
            'http://127.0.0.1:5500',
            'http://localhost:3002',
            'http://localhost:3000',
            'http://localhost:5000',
            'http://127.0.0.1:3002'
          ];
          if (allowedOrigins.includes(origin)) {
            callback(null, true);
          } else {
            console.warn(`CORS: Origin ${origin} not allowed`);
            callback(null, true); // Allow for development
          }
        },
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        credentials: true
      }));
    
      // JSON body parser
      this.app.use(express.json({ limit: '10mb', type: 'application/json' }));
      this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
      // Serve static files with no-cache for HTML
      this.app.use(express.static(path.join(__dirname), {
        setHeaders: (res, path) => {
          if (path.endsWith('.html')) {
            res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
          }
        }
      }));
    
      // Adjust rate limiting for API routes
      const apiLimiter = rateLimit({
        windowMs: 1 * 60 * 1000, // 1 minute window (changed from 15 minutes)
        max: 200, // Increased limit from 100 to 200 requests per window
        message: { 
          error: 'Too many requests from this IP, please try again later',
          retryAfter: 'Wait 60 seconds before trying again'
        },
        standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
        legacyHeaders: false,
        skip: (req) => {
          // Skip rate limiting for health checks and analytics
          return req.path === '/health' || req.path === '/api/analytics';
        }
      });

      // Apply rate limiter only to database operations
      this.app.use('/api/database-types', apiLimiter);
      this.app.use('/api/databases', apiLimiter);
      this.app.use('/api/query', apiLimiter);
    
      // Separate rate limiter for analytics with more lenient limits
      const analyticsLimiter = rateLimit({
          windowMs: 5 * 60 * 1000, // 5 minutes
          max: 1000, // allow more requests for analytics
          message: { 
              error: 'Too many analytics requests',
              retryAfter: 'Wait a few minutes before trying again'
          },
          standardHeaders: true,
          legacyHeaders: false
      });

      // Apply different rate limits to different endpoints
      this.app.use('/api/analytics', analyticsLimiter);
      this.app.use('/health', analyticsLimiter);
    
      // Request logging middleware
      this.app.use((req, res, next) => {
        const start = performance.now();
        console.log(` ${req.method} ${req.path} - ${req.ip}`);
        res.on('finish', () => {
          const duration = performance.now() - start;
          this.logger.log(req, res, duration);
          console.log(`📤 ${req.method} ${req.path} - ${res.statusCode} (${Math.round(duration)}ms)`);
        });
        next();
      });
    }

    async handleSelectDatabase(req, res) {
      try {
          const response = await axios.post(`${this.backendUrl}/api/select-database`, req.body, { timeout: 10000 });
          console.log(`📊 Selected database: ${req.body.database}`);
          res.json(response.data);
      } catch (error) {
          console.error('Database selection error:', error.message);
          this.handleAxiosError(error, res, 'Failed to select database');
      }
}

    async handleTableData(req, res) {
      try {
          const { session_id, table, limit = 100 } = req.query;
          if (!session_id || !table) {
              return res.status(400).json({ 
                  success: false, 
                  error: 'Session ID and table name required' 
              });
          }
        
          const response = await axios.get(`${this.backendUrl}/api/table-data`, { 
              params: { session_id, table, limit }, 
              timeout: 10000 
          });
        
          console.log(`📋 Fetched table data for ${table}`);
          res.json(response.data);
      } catch (error) {
          console.error('Table data fetch error:', error.message);
          this.handleAxiosError(error, res, 'Failed to fetch table data');
      }
}

    async handleSelectTable(req, res) {
      try {
          console.log('📋 Table selection request:', req.body);
        
          const response = await axios.post(`${this.backendUrl}/api/select-table`, req.body, { timeout: 10000 });
          console.log(`✅ Selected table: ${req.body.table_name}`);
          res.json(response.data);
      } catch (error) {
          console.error('Table selection error:', error.message);
          this.handleAxiosError(error, res, 'Failed to select table');
      }
}

    async handleGetSelectedTable(req, res) {
      try {
          const session_id = req.query.session_id;
          if (!session_id) {
              return res.status(400).json({ 
                  success: false, 
                  error: 'Session ID required' 
              });
          }
        
          const response = await axios.get(`${this.backendUrl}/api/get-selected-table`, { 
              params: { session_id }, 
              timeout: 10000 
          });
        
          console.log(`📋 Current selected table: ${response.data.selected_table}`);
          res.json(response.data);
      } catch (error) {
          console.error('Get selected table error:', error.message);
          this.handleAxiosError(error, res, 'Failed to get selected table');
      }
}

    async handleDatabaseConnect(req, res) {
      try {
          console.log('📥 Database connect request received:', req.body);
        
          const sessionId = req.body.session_id || require('crypto').randomUUID();
        
          const requestData = {
              session_id: sessionId,
              type: req.body.type,
              config: req.body.config
          };

          const response = await axios.post(`${this.backendUrl}/api/connect`, requestData, { 
              timeout: 30000 
          });

          res.json({
              ...response.data,
              session_id: sessionId
          });
        
      } catch (error) {
          console.error('❌ Database connection error:', error.message);
          this.handleAxiosError(error, res, 'Database connection failed');
      }
}

    async handleGetDatabases(req, res) {
      try {
          const sessionId = req.query.session_id;
          console.log('📋 Fetching databases for session:', sessionId);
        
          if (!sessionId) {
              return res.status(400).json({ 
                  success: false, 
                  error: 'Session ID required' 
              });
          }
        
          const response = await axios.get(`${this.backendUrl}/api/databases`, { 
              params: { session_id: sessionId }, 
              timeout: 10000 
          });
        
          console.log('✅ Databases response:', response.data);
          res.json(response.data);
        
      } catch (error) {
          console.error('❌ Get databases error:', error.message);
          this.handleAxiosError(error, res, 'Failed to fetch databases');
      }
}

    async handleGetTables(req, res) {
      try {
          const sessionId = req.query.session_id;
          const database = req.query.database;
        
          console.log('📋 Fetching tables for session:', sessionId, 'database:', database);
        
          if (!sessionId || !database) {
              return res.status(400).json({ 
                  success: false, 
                  error: 'Session ID and database required' 
              });
          }
        
          const response = await axios.get(`${this.backendUrl}/api/tables`, { 
              params: { session_id: sessionId, database: database }, 
              timeout: 10000 
          });
        
          console.log('✅ Tables response:', response.data);
          res.json(response.data);
        
      } catch (error) {
          console.error('❌ Get tables error:', error.message);
          this.handleAxiosError(error, res, 'Failed to fetch tables');
      }
}

    async handleGetTableSchema(req, res) {
      try {
          const sessionId = req.query.session_id;
          const database = req.query.database;
          const table = req.query.table;
        
          console.log('📋 Fetching schema for:', { sessionId, database, table });
        
          if (!sessionId || !database || !table) {
              return res.status(400).json({ 
                  success: false, 
                  error: 'Session ID, database, and table required' 
              });
          }
        
          const response = await axios.get(`${this.backendUrl}/api/table-schema`, { 
              params: { session_id: sessionId, database: database, table: table }, 
              timeout: 10000 
          });
        
          console.log('✅ Schema response:', response.data);
          res.json(response.data);
        
      } catch (error) {
          console.error('❌ Get schema error:', error.message);
          this.handleAxiosError(error, res, 'Failed to fetch table schema');
      }
}

    async handleGenerateAndExecuteQuery(req, res) {
        try {
            const { session_id, database, table, question, execute = true } = req.body;
            
            // Get the database type from the request or fetch it from connection info
            let db_type = req.body.db_type;
            
            console.log('🤖 Generate and execute query:', { session_id, database, table, question, db_type });
            
            if (!session_id || !database || !question) {
                return res.status(400).json({ 
                    success: false, 
                    error: 'Session ID, database, and question required' 
                });
            }
            
            // If db_type is not provided, try to get it from connection info
            if (!db_type) {
                try {
                    // Make a request to get connection info
                    const connInfoResponse = await axios.get(`${this.backendUrl}/api/connection-info`, {
                        params: { session_id },
                        timeout: 5000
                    });
                    
                    if (connInfoResponse.data.success) {
                        db_type = connInfoResponse.data.connection_info.type;
                        console.log(`Retrieved database type: ${db_type}`);
                    } else {
                        // Default to mysql if we can't determine
                        db_type = 'mysql';
                        console.log('Using default database type: mysql');
                    }
                } catch (error) {
                    console.warn('Failed to get connection info, using default type mysql');
                    db_type = 'mysql';
                }
            }
            
            const requestData = {
                session_id,
                database,
                table,
                question,
                execute,
                db_type
            };
            
            const response = await axios.post(`${this.backendUrl}/api/ai-query`, requestData, { 
                timeout: 60000 
            });
            
            console.log('✅ AI Query response:', response.data);
            res.json(response.data);
            
        } catch (error) {
            console.error('❌ AI Query error:', error.message);
            this.handleAxiosError(error, res, 'Failed to generate/execute query');
        }
    }

    setupRoutes() {
      // Health check
      this.app.get('/health', (req, res) => this.handleHealth(req, res));
    
      // Database connection
      this.app.post('/api/database/connect', (req, res) => this.handleDatabaseConnect(req, res));
    
      // Get databases
      this.app.get('/api/databases', (req, res) => this.handleGetDatabases(req, res));
    
      // Select database
      this.app.post('/api/select-database', (req, res) => this.handleSelectDatabase(req, res));
    
      // Get schema
      this.app.get('/api/schema', (req, res) => this.handleGetSchema(req, res));
    
      // Get tables
      this.app.get('/api/tables', (req, res) => this.handleGetTables(req, res));
    
      // Get table schema
      this.app.get('/api/table-schema', (req, res) => this.handleGetTableSchema(req, res));
    
      // Get table data
      this.app.get('/api/table-data', (req, res) => this.handleTableData(req, res));
    
      // Generate and execute AI query
      this.app.post('/api/ai-query', (req, res) => this.handleGenerateAndExecuteQuery(req, res));
    
      // Execute custom query
      this.app.post('/api/execute-query', (req, res) => this.handleExecuteQuery(req, res));
    
      // Static files
      this.app.get('/', (req, res) => res.sendFile(path.join(__dirname, 'index.html')));
    
      // 404 handler
      this.app.use('*', (req, res) => {
          console.log('❌ 404 - Route not found:', req.originalUrl);
          if (req.originalUrl.startsWith('/api/')) {
              res.status(404).json({ 
                  success: false,
                  error: 'API endpoint not found', 
                  path: req.originalUrl
              });
          } else {
              res.sendFile(path.join(__dirname, 'index.html'));
          }
      });
    }

    setupErrorHandling() {
      this.app.use((err, req, res, next) => {
        console.error('💥 Global error:', err);
        if (err.type === 'entity.parse.failed') {
          return res.status(400).json({ error: 'Invalid JSON in request body', details: err.message });
        }
        res.status(500).json({
          error: 'Internal server error',
          details: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
        });
      });
    }

    async handleHealth(req, res) {
      try {
        const backendHealth = await axios.get(`${this.backendUrl}/health`, { timeout: 3000 }).catch(err => {
          console.warn('Backend health check failed:', err.message);
          return { data: { status: 'unhealthy', error: err.code === 'ECONNREFUSED' ? 'Backend server not running' : err.message } };
        });
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          middleware: {
            uptime: Math.floor(process.uptime()),
            memory: {
              used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB',
              total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + 'MB'
            },
            cache: this.cache.getStats(),
            requests: this.logger.getStats()
          },
          backend: backendHealth.data
        });
      } catch (error) {
        console.error('Health check error:', error.message);
        res.status(500).json({ status: 'unhealthy', timestamp: new Date().toISOString(), error: error.message });
      }
    }

    async handleDatabaseTypes(req, res) {
      try {
        const response = await axios.get(`${this.backendUrl}/api/database-types`, { timeout: 10000 });
        console.log('📋 Fetched database types');
        res.json(response.data);
      } catch (error) {
        console.error('Database types fetch error:', error.message);
        this.handleAxiosError(error, res, 'Failed to fetch database types');
      }
    }

    async handleDatabases(req, res) {
      try {
          const session_id = req.query.session_id;
          console.log('📋 Fetching databases for session:', session_id);
        
          if (!session_id) {
              return res.status(400).json({ 
                  success: false, 
                  error: 'Session ID required' 
              });
          }
        
          const response = await axios.get(`${this.backendUrl}/api/databases`, { 
              params: { session_id }, 
              timeout: 10000 
          });
        
          console.log('✅ Backend databases response:', response.data);
          res.json(response.data);
        
      } catch (error) {
          console.error('❌ Database fetch error:', error.message);
        
          if (error.response) {
              console.error('Backend error response:', error.response.data);
              res.status(error.response.status).json({
                  success: false,
                  message: error.response.data.message || 'Failed to fetch databases',
                  details: error.response.data
              });
          } else if (error.code === 'ECONNREFUSED') {
              res.status(503).json({
                  success: false,
                  message: 'Backend server is not running',
                  details: 'Connection refused to Python backend'
              });
          } else {
              res.status(500).json({
                  success: false,
                  message: 'Failed to fetch databases',
                  details: error.message
              });
          }
      }
    }

    async handleDatasets(req, res) {
      try {
        const session_id = req.query.session_id;
        const database = req.query.database;
        if (!session_id || !database) {
          return res.status(400).json({ error: 'Session ID and database required' });
        }
        const response = await axios.get(`${this.backendUrl}/api/datasets`, { params: { session_id, database }, timeout: 10000 });
        console.log(`📊 Fetched datasets for session ${session_id} database ${database}`);
        res.json(response.data);
      } catch (error) {
        console.error('Datasets fetch error:', error.message);
        this.handleAxiosError(error, res, 'Failed to fetch datasets');
      }
    }

    async handleDatabaseConnect(req, res) {
      try {
          console.log('📥 Database connect request received:', req.body);
        
          const schema = Joi.object({
              type: Joi.string().required(),
              database: Joi.string().required(),
              config: Joi.object({
                  host: Joi.string().default('localhost'),
                  port: Joi.number().required(),
                  username: Joi.string().allow(''),
                  password: Joi.string().allow(''),
                  database_path: Joi.string().optional()
              }).required()
          });

          const { error } = schema.validate(req.body);
          if (error) {
              console.error('❌ Validation error:', error.details[0].message);
              return res.status(400).json({ 
                  success: false, 
                  error: error.details[0].message 
              });
          }

          const { type, database, config } = req.body;
          console.log(`🔗 Attempting to connect to ${type} database: ${database}`);

          const response = await axios.post(`${this.backendUrl}/api/connect`, {
              type,
              database,
              config
          }, { timeout: 30000 });

          console.log('✅ Backend response:', response.data);
          res.json(response.data);
        
      } catch (error) {
          console.error('❌ Database connection error:', error.message);
        
          if (error.response) {
              // Backend returned an error response
              console.error('Backend error response:', error.response.data);
              res.status(error.response.status).json({
                  success: false,
                  message: error.response.data.message || 'Backend connection failed',
                  details: error.response.data
              });
          } else if (error.code === 'ECONNREFUSED') {
              // Backend server not running
              res.status(503).json({
                  success: false,
                  message: 'Backend server is not running. Please start the Python server on port 5001.',
                  details: 'Connection refused to backend'
              });
          } else {
              // Other errors
              res.status(500).json({
                  success: false,
                  message: 'Connection failed',
                  details: error.message
              });
          }
      }
    }

    async handleSchemaRequest(req, res) {
        try {
            const { session_id, database } = req.query;
            
            if (!session_id || !database) {
                return res.status(400).json({ 
                    success: false, 
                    error: 'Session ID and database required' 
                });
            }
            
            const response = await axios.get(`${this.backendUrl}/api/schema`, {
                params: { session_id, database },
                timeout: 10000
            });
            
            // Enhance schema with additional metadata if available
            if (response.data.success && response.data.schema) {
                const schema = response.data.schema;
                
                // Add table descriptions and column types if not present
                Object.keys(schema).forEach(tableName => {
                    if (!schema[tableName].description) {
                        schema[tableName].description = `Table: ${tableName}`;
                    }
                    
                    // Ensure column info is structured properly
                    if (schema[tableName].columns && !schema[tableName].columnInfo) {
                        schema[tableName].columnInfo = {};
                        schema[tableName].columns.forEach(column => {
                            schema[tableName].columnInfo[column] = {
                                name: column,
                                type: 'unknown',
                                nullable: true
                            };
                        });
                    }
                });
                
                response.data.schema = schema;
            }
            
            console.log('✅ Schema response enhanced');
            res.json(response.data);
            
        } catch (error) {
            console.error('❌ Schema error:', error.message);
            this.handleAxiosError(error, res, 'Failed to get schema');
        }
    }

    async handleQuery(req, res) {
      const startTime = performance.now();
      const schema = Joi.object({
        query: Joi.string().required(),
        session_id: Joi.string().required()
      });
      const { error } = schema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }
      const { query, session_id } = req.body;
      try {
        const response = await axios.post(`${this.backendUrl}/api/query`, { query, session_id }, { timeout: 60000 });
        const duration = performance.now() - startTime;
        console.log('\n===================');
        console.log('📊 Query Execution Report');
        console.log('Query:', query);
        console.log('Execution Time:', duration.toFixed(2), 'ms');
        console.log('Results Count:', response.data.count || 0);
        console.log('===================\n');
        res.json({ ...response.data, middlewareTime: Math.round(duration) });
      } catch (error) {
        const duration = performance.now() - startTime;
        console.error('❌ Query execution error:', error.message);
        this.handleAxiosError(error, res, 'Query processing failed', Math.round(duration));
      }
    }

    async handleAnalytics(req, res) {
      try {
          const backendAnalytics = await axios.get(`${this.backendUrl}/api/analytics`, { timeout: 5000 }).catch(err => {
              console.warn('Backend analytics fetch failed:', err.message);
              return { data: { success: true, message: 'Analytics not available' } };
          });
          res.json(backendAnalytics.data);
      } catch (error) {
          console.error('Analytics error:', error.message);
          res.status(500).json({ error: 'Failed to fetch analytics', details: error.message });
      }
    }

    async handleSessions(req, res) {
      try {
          const response = await axios.get(`${this.backendUrl}/api/sessions`, { timeout: 5000 });
          res.json(response.data);
      } catch (error) {
          console.error('Sessions fetch error:', error.message);
          this.handleAxiosError(error, res, 'Failed to fetch sessions');
      }
    }

    handleLogs(req, res) {
      try {
          const limit = parseInt(req.query.limit) || 50;
          res.json({ 
              logs: this.logger.getRecentLogs(limit), 
              stats: this.logger.getStats() 
          });
      } catch (error) {
          console.error('Logs fetch error:', error.message);
          res.status(500).json({ error: 'Failed to fetch logs', details: error.message });
      }
    }

    async handleCacheClear(req, res) {
      try {
          this.cache.clear();
          res.json({ 
              success: true, 
              message: 'Cache cleared successfully' 
          });
      } catch (error) {
          console.error('Cache clear error:', error.message);
          res.status(500).json({ error: 'Failed to clear cache', details: error.message });
      }
    }

    handleAxiosError(error, res, message, duration = null) {
      const errorResponse = { error: message, details: error.response?.data || error.message, success: false };
      if (duration !== null) {
        errorResponse.middlewareTime = duration;
      }
      let statusCode = 500;
      if (error.code === 'ECONNREFUSED') {
        statusCode = 503;
        errorResponse.details = 'Backend server is not running';
      } else if (error.response?.status) {
        statusCode = error.response.status;
      }
      res.status(statusCode).json(errorResponse);
    }

    async handleAiQuery(req, res) {
        try {
            const { question, session_id, database, db_type, execute } = req.body;
            console.log(`🤖 Processing AI query for session ${session_id}: "${question}" on database ${database} (type: ${db_type})`);

            const response = await axios.post(`${this.backendUrl}/api/ai-query`, {
                question,
                session_id,
                database,
                db_type, // Pass the database type to the backend
                execute
            }, { timeout: 60000 });

            console.log('✅ AI Query response received from backend');
            res.json(response.data);
        } catch (error) {
            console.error('AI query error:', error.message);
            this.handleAxiosError(error, res, 'Failed to process AI query');
        }
    }

    async handleGetSchema(req, res) {
      try {
          const response = await axios.get(`${this.backendUrl}/api/schema`, {
              params: req.query,
              timeout: 10000
          });
          console.log('✅ Schema response received');
          res.json(response.data);
      } catch (error) {
          console.error('❌ Schema error:', error.message);
          this.handleAxiosError(error, res, 'Failed to fetch schema');
      }
    }

    start(port = 3002) {
      const server = this.app.listen(port, '0.0.0.0', () => {
        console.log(`
🚀 SQL Chat Middleware Server Started
===========================================
🌐 Server URL: http://localhost:${port}
🔗 Backend URL: ${this.backendUrl}
📊 Health Check: http://localhost:${port}/health
🎯 Main App: http://localhost:${port}/
===========================================
        `);
      });
      server.on('error', (error) => {
        if (error.code === 'EADDRINUSE') {
          console.error(`❌ Port ${port} is already in use. Please use a different port.`);
          process.exit(1);
        } else {
          console.error('❌ Server error:', error);
        }
      });
      return server;
    }
}

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down middleware server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Start the middleware server
const middleware = new SQLChatMiddleware();
middleware.start();
