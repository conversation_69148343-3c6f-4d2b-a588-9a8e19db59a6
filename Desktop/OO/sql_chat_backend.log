2025-06-20 01:06:55,907 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:06:55,910 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:15:58,876 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 318f8d92-ce3b-4727-9ae5-9d7fc61cb684
2025-06-20 01:15:58,882 - __main__ - ERROR - [m.py:795] - Connection error: name 'sql_manager' is not defined
2025-06-20 01:15:59,009 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:15:59] "[35m[1mPOST /api/connect HTTP/1.1[0m" 500 -
2025-06-20 01:17:44,551 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:19:13,582 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***************:5001
2025-06-20 01:19:13,582 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-20 01:19:13,584 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:19:13,988 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:19:13,989 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:19:21,456 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session ef033e14-2772-4e28-8645-a5ebfd38954e
2025-06-20 01:19:21,573 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:19:21,574 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-20 01:19:21,576 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-20 01:19:21,594 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:19:21,595 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-20 01:19:21,599 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-20 01:19:21,650 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session ef033e14-2772-4e28-8645-a5ebfd38954e
2025-06-20 01:19:21,652 - __main__ - INFO - [m.py:410] - Connection stored for session ef033e14-2772-4e28-8645-a5ebfd38954e: mysql/
2025-06-20 01:19:21,655 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:19:21] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:19:21,832 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:19:21] "[33mGET /api/databases?session_id=ef033e14-2772-4e28-8645-a5ebfd38954e HTTP/1.1[0m" 404 -
2025-06-20 01:19:40,937 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session b9ddf9c3-004b-4e4f-bdc7-ecb8beec2903
2025-06-20 01:19:40,944 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session b9ddf9c3-004b-4e4f-bdc7-ecb8beec2903
2025-06-20 01:19:40,945 - __main__ - INFO - [m.py:410] - Connection stored for session b9ddf9c3-004b-4e4f-bdc7-ecb8beec2903: mysql/
2025-06-20 01:19:40,947 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:19:40] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:19:41,151 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:19:41] "[33mGET /api/databases?session_id=b9ddf9c3-004b-4e4f-bdc7-ecb8beec2903 HTTP/1.1[0m" 404 -
2025-06-20 01:32:32,749 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:32:33,892 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:32:35,196 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:32:35,199 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:32:41,061 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session a7e8cdb0-d089-4df9-b787-96463aa027fd
2025-06-20 01:32:41,209 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:32:41,209 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-20 01:32:41,209 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-20 01:32:41,212 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:32:41,212 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-20 01:32:41,213 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-20 01:32:41,232 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session a7e8cdb0-d089-4df9-b787-96463aa027fd
2025-06-20 01:32:41,232 - __main__ - INFO - [m.py:410] - Connection stored for session a7e8cdb0-d089-4df9-b787-96463aa027fd: mysql/
2025-06-20 01:32:41,233 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:41] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:32:41,585 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:41] "GET /api/databases?session_id=a7e8cdb0-d089-4df9-b787-96463aa027fd HTTP/1.1" 200 -
2025-06-20 01:32:43,150 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session b237dc5e-f706-4a00-8dfb-7ef8e0bae74d
2025-06-20 01:32:43,153 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session b237dc5e-f706-4a00-8dfb-7ef8e0bae74d
2025-06-20 01:32:43,154 - __main__ - INFO - [m.py:410] - Connection stored for session b237dc5e-f706-4a00-8dfb-7ef8e0bae74d: mysql/
2025-06-20 01:32:43,154 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:43] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:32:43,249 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:43] "GET /api/databases?session_id=b237dc5e-f706-4a00-8dfb-7ef8e0bae74d HTTP/1.1" 200 -
2025-06-20 01:32:51,906 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session af68093d-5030-4575-be92-e1a2a728ba4b
2025-06-20 01:32:51,912 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session af68093d-5030-4575-be92-e1a2a728ba4b
2025-06-20 01:32:51,913 - __main__ - INFO - [m.py:410] - Connection stored for session af68093d-5030-4575-be92-e1a2a728ba4b: mysql/
2025-06-20 01:32:51,915 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:51] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:32:52,038 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:52] "GET /api/databases?session_id=af68093d-5030-4575-be92-e1a2a728ba4b HTTP/1.1" 200 -
2025-06-20 01:34:01,862 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session f2a46d9d-1c1a-4a47-8f00-d347321b7464
2025-06-20 01:34:01,870 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session f2a46d9d-1c1a-4a47-8f00-d347321b7464
2025-06-20 01:34:01,871 - __main__ - INFO - [m.py:410] - Connection stored for session f2a46d9d-1c1a-4a47-8f00-d347321b7464: mysql/
2025-06-20 01:34:01,882 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:34:01] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:34:02,113 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:34:02] "GET /api/databases?session_id=f2a46d9d-1c1a-4a47-8f00-d347321b7464 HTTP/1.1" 200 -
2025-06-20 01:35:33,959 - __main__ - INFO - [m.py:766] - Attempting to connect to mongodb database for session 5ec224c3-6b1b-4a7b-8668-fb87fb7f5e12
2025-06-20 01:35:33,978 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-20 01:35:33,981 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-19T20:05:33.980929+00:00', 'session_id': '5ec224c3-6b1b-4a7b-8668-fb87fb7f5e12', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-20 01:35:33,983 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:35:33] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-20 01:36:12,578 - __main__ - INFO - [m.py:766] - Attempting to connect to mongodb database for session e519001a-32a5-492d-9c18-2585f2d84096
2025-06-20 01:36:12,583 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-20 01:36:12,587 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-19T20:06:12.586580+00:00', 'session_id': 'e519001a-32a5-492d-9c18-2585f2d84096', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-20 01:36:12,590 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:36:12] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-20 01:36:52,165 - __main__ - INFO - [m.py:766] - Attempting to connect to mongodb database for session 8f535ac8-b578-42c3-9b29-8d584dc37b4c
2025-06-20 01:36:52,426 - __main__ - ERROR - [m.py:285] - Connection error: Authentication failed., full error: {'ok': 0.0, 'errmsg': 'Authentication failed.', 'code': 18, 'codeName': 'AuthenticationFailed'}
2025-06-20 01:36:52,430 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': "Authentication failed., full error: {'ok': 0.0, 'errmsg': 'Authentication failed.', 'code': 18, 'codeName': 'AuthenticationFailed'}", 'timestamp': '2025-06-19T20:06:52.430552+00:00', 'session_id': '8f535ac8-b578-42c3-9b29-8d584dc37b4c', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-20 01:36:52,433 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:36:52] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-20 01:37:06,398 - __main__ - INFO - [m.py:766] - Attempting to connect to mongodb database for session a958f1c5-77d2-40c4-847f-101439004a9a
2025-06-20 01:37:06,399 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-20 01:37:06,399 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-19T20:07:06.399765+00:00', 'session_id': 'a958f1c5-77d2-40c4-847f-101439004a9a', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-20 01:37:06,401 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:37:06] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-20 01:39:32,999 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:39:34,051 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:39:35,996 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:39:35,998 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:39:43,749 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 5ba013d1-4901-4543-b7a8-b233e896b94b
2025-06-20 01:39:43,839 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:39:43,840 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-20 01:39:43,841 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-20 01:39:43,846 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:39:43,847 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-20 01:39:43,849 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-20 01:39:43,875 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 5ba013d1-4901-4543-b7a8-b233e896b94b
2025-06-20 01:39:43,876 - __main__ - INFO - [m.py:410] - Connection stored for session 5ba013d1-4901-4543-b7a8-b233e896b94b: mysql/
2025-06-20 01:39:43,880 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:39:43] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:39:44,102 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:39:44] "GET /api/databases?session_id=5ba013d1-4901-4543-b7a8-b233e896b94b HTTP/1.1" 200 -
2025-06-20 01:41:30,126 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:41:30,297 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:41:31,322 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:41:31,323 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:41:32,379 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:41:32,480 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:41:33,132 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:41:33,133 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:41:48,813 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session f100c3bc-1242-4263-80f0-6bb2e1a77340
2025-06-20 01:41:48,864 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:41:48,865 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-20 01:41:48,866 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-20 01:41:48,871 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:41:48,873 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-20 01:41:48,878 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-20 01:41:48,917 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session f100c3bc-1242-4263-80f0-6bb2e1a77340
2025-06-20 01:41:48,919 - __main__ - INFO - [m.py:410] - Connection stored for session f100c3bc-1242-4263-80f0-6bb2e1a77340: mysql/
2025-06-20 01:41:48,927 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:41:48] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:41:49,163 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:41:49] "GET /api/databases?session_id=f100c3bc-1242-4263-80f0-6bb2e1a77340 HTTP/1.1" 200 -
2025-06-20 01:53:10,224 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 29efdf6c-5a26-4f7b-97c2-87820c1552d1
2025-06-20 01:53:10,342 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 29efdf6c-5a26-4f7b-97c2-87820c1552d1
2025-06-20 01:53:10,345 - __main__ - INFO - [m.py:410] - Connection stored for session 29efdf6c-5a26-4f7b-97c2-87820c1552d1: mysql/
2025-06-20 01:53:10,364 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:53:10] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:53:10,726 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:53:10] "GET /api/databases?session_id=29efdf6c-5a26-4f7b-97c2-87820c1552d1 HTTP/1.1" 200 -
2025-06-20 01:55:56,300 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 70278f36-b341-4eb8-ac60-8526a95b80a2
2025-06-20 01:55:56,386 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 70278f36-b341-4eb8-ac60-8526a95b80a2
2025-06-20 01:55:56,389 - __main__ - INFO - [m.py:410] - Connection stored for session 70278f36-b341-4eb8-ac60-8526a95b80a2: mysql/
2025-06-20 01:55:56,411 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:55:56] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:55:56,632 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:55:56] "GET /api/databases?session_id=70278f36-b341-4eb8-ac60-8526a95b80a2 HTTP/1.1" 200 -
2025-06-20 01:55:58,784 - __main__ - INFO - [m.py:486] - Updated selected database for session 70278f36-b341-4eb8-ac60-8526a95b80a2: us_data
2025-06-20 01:55:58,784 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:55:58] "POST /api/select-database HTTP/1.1" 200 -
2025-06-20 01:58:45,721 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session c0544786-85ec-4f7b-ae92-96140d0eeab6
2025-06-20 01:58:45,829 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session c0544786-85ec-4f7b-ae92-96140d0eeab6
2025-06-20 01:58:45,832 - __main__ - INFO - [m.py:410] - Connection stored for session c0544786-85ec-4f7b-ae92-96140d0eeab6: mysql/
2025-06-20 01:58:45,836 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:58:45] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:58:46,045 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:58:46] "GET /api/databases?session_id=c0544786-85ec-4f7b-ae92-96140d0eeab6 HTTP/1.1" 200 -
2025-06-20 01:58:48,730 - __main__ - INFO - [m.py:486] - Updated selected database for session c0544786-85ec-4f7b-ae92-96140d0eeab6: us_data
2025-06-20 01:58:48,732 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:58:48] "POST /api/select-database HTTP/1.1" 200 -
2025-06-20 01:59:32,355 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session aaa566fb-9c99-459a-8253-09e3f3b5951e
2025-06-20 01:59:32,364 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session aaa566fb-9c99-459a-8253-09e3f3b5951e
2025-06-20 01:59:32,365 - __main__ - INFO - [m.py:410] - Connection stored for session aaa566fb-9c99-459a-8253-09e3f3b5951e: mysql/
2025-06-20 01:59:32,369 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:59:32] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:59:32,569 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:59:32] "GET /api/databases?session_id=aaa566fb-9c99-459a-8253-09e3f3b5951e HTTP/1.1" 200 -
2025-06-20 01:59:35,840 - __main__ - INFO - [m.py:486] - Updated selected database for session aaa566fb-9c99-459a-8253-09e3f3b5951e: us_data
2025-06-20 01:59:35,841 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:59:35] "POST /api/select-database HTTP/1.1" 200 -
2025-06-20 01:59:35,927 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:59:35] "[31m[1mGET /api/schema?session_id=aaa566fb-9c99-459a-8253-09e3f3b5951e HTTP/1.1[0m" 400 -
2025-06-20 02:03:53,111 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 1b809611-0d71-4293-8e15-16a28d312a1a
2025-06-20 02:03:53,170 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 1b809611-0d71-4293-8e15-16a28d312a1a
2025-06-20 02:03:53,172 - __main__ - INFO - [m.py:410] - Connection stored for session 1b809611-0d71-4293-8e15-16a28d312a1a: mysql/
2025-06-20 02:03:53,178 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 02:03:53] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 02:03:53,476 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 02:03:53] "GET /api/databases?session_id=1b809611-0d71-4293-8e15-16a28d312a1a HTTP/1.1" 200 -
2025-06-20 02:03:57,271 - __main__ - INFO - [m.py:486] - Updated selected database for session 1b809611-0d71-4293-8e15-16a28d312a1a: us_data
2025-06-20 02:03:57,273 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 02:03:57] "POST /api/select-database HTTP/1.1" 200 -
2025-06-20 02:03:57,676 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 02:03:57] "GET /api/schema?session_id=1b809611-0d71-4293-8e15-16a28d312a1a&database=us_data HTTP/1.1" 200 -
2025-06-27 12:55:10,569 - __main__ - INFO - GROQ API key loaded successfully (starts with: gsk_ufde7D...)
2025-06-27 12:55:10,700 - __main__ - INFO - GROQ API client initialized successfully with key: gsk_ufde7D...
2025-06-27 12:55:11,028 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-27 12:55:11,032 - __main__ - INFO - GROQ API key validation successful
2025-06-27 12:55:11,076 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 12:55:11,076 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-27 12:55:11,081 - werkzeug - INFO -  * Restarting with stat
2025-06-27 12:55:11,645 - __main__ - INFO - GROQ API key loaded successfully (starts with: gsk_ufde7D...)
2025-06-27 12:55:11,699 - __main__ - INFO - GROQ API client initialized successfully with key: gsk_ufde7D...
2025-06-27 12:55:11,982 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-27 12:55:11,987 - __main__ - INFO - GROQ API key validation successful
2025-06-27 12:55:12,031 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 12:55:12,179 - werkzeug - INFO -  * Debugger PIN: 561-057-362
2025-06-27 12:55:56,956 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 12:55:56,956 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-27 12:55:56,957 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 12:55:57,422 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 12:55:57,423 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 12:56:31,085 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 63ee9dd7-8392-4c21-be72-2c9fdff1e348
2025-06-27 12:56:31,216 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 12:56:31,216 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 12:56:31,216 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 12:56:31,230 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 12:56:31,231 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 12:56:31,236 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 12:56:31,292 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 63ee9dd7-8392-4c21-be72-2c9fdff1e348
2025-06-27 12:56:31,293 - __main__ - INFO - [m.py:410] - Connection stored for session 63ee9dd7-8392-4c21-be72-2c9fdff1e348: mysql/
2025-06-27 12:56:31,298 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:56:31] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 12:56:31,611 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:56:31] "GET /api/databases?session_id=63ee9dd7-8392-4c21-be72-2c9fdff1e348 HTTP/1.1" 200 -
2025-06-27 12:56:35,345 - __main__ - INFO - [m.py:486] - Updated selected database for session 63ee9dd7-8392-4c21-be72-2c9fdff1e348: us_data
2025-06-27 12:56:35,346 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:56:35] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 12:56:35,490 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:56:35] "GET /api/schema?session_id=63ee9dd7-8392-4c21-be72-2c9fdff1e348&database=us_data HTTP/1.1" 200 -
2025-06-27 12:57:21,826 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 5e25faff-98b9-40b1-85d8-aed3a1cfb6e6
2025-06-27 12:57:21,830 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 5e25faff-98b9-40b1-85d8-aed3a1cfb6e6
2025-06-27 12:57:21,831 - __main__ - INFO - [m.py:410] - Connection stored for session 5e25faff-98b9-40b1-85d8-aed3a1cfb6e6: mysql/
2025-06-27 12:57:21,834 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:57:21] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 12:57:21,921 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:57:21] "GET /api/databases?session_id=5e25faff-98b9-40b1-85d8-aed3a1cfb6e6 HTTP/1.1" 200 -
2025-06-27 12:57:25,709 - __main__ - INFO - [m.py:486] - Updated selected database for session 5e25faff-98b9-40b1-85d8-aed3a1cfb6e6: us_data
2025-06-27 12:57:25,710 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:57:25] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 12:57:25,816 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:57:25] "GET /api/schema?session_id=5e25faff-98b9-40b1-85d8-aed3a1cfb6e6&database=us_data HTTP/1.1" 200 -
2025-06-27 14:02:38,877 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 14:02:38,878 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-27 14:02:38,879 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 14:02:39,356 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 14:02:39,356 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 14:02:57,642 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session e9d83772-a60f-43ed-bb73-2dc80662f0d1
2025-06-27 14:02:57,817 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:02:57,818 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 14:02:57,818 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 14:02:57,826 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:02:57,826 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 14:02:57,828 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 14:02:57,872 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session e9d83772-a60f-43ed-bb73-2dc80662f0d1
2025-06-27 14:02:57,873 - __main__ - INFO - [m.py:410] - Connection stored for session e9d83772-a60f-43ed-bb73-2dc80662f0d1: mysql/
2025-06-27 14:02:57,876 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:02:57] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:02:58,077 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:02:58] "GET /api/databases?session_id=e9d83772-a60f-43ed-bb73-2dc80662f0d1 HTTP/1.1" 200 -
2025-06-27 14:03:00,812 - __main__ - INFO - [m.py:486] - Updated selected database for session e9d83772-a60f-43ed-bb73-2dc80662f0d1: us_data
2025-06-27 14:03:00,813 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:03:00] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:03:01,036 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:03:01] "GET /api/schema?session_id=e9d83772-a60f-43ed-bb73-2dc80662f0d1&database=us_data HTTP/1.1" 200 -
2025-06-27 14:26:28,592 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session df51e1c3-eedf-40c9-895d-58cf0ecd1ffa
2025-06-27 14:26:28,741 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session df51e1c3-eedf-40c9-895d-58cf0ecd1ffa
2025-06-27 14:26:28,744 - __main__ - INFO - [m.py:410] - Connection stored for session df51e1c3-eedf-40c9-895d-58cf0ecd1ffa: mysql/
2025-06-27 14:26:28,772 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:28] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:26:29,116 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:29] "GET /api/databases?session_id=df51e1c3-eedf-40c9-895d-58cf0ecd1ffa HTTP/1.1" 200 -
2025-06-27 14:26:34,321 - __main__ - INFO - [m.py:486] - Updated selected database for session df51e1c3-eedf-40c9-895d-58cf0ecd1ffa: us_data
2025-06-27 14:26:34,324 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:34] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:26:34,497 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:34] "GET /api/tables?session_id=df51e1c3-eedf-40c9-895d-58cf0ecd1ffa&database=us_data HTTP/1.1" 200 -
2025-06-27 14:26:34,580 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:34] "GET /api/schema?session_id=df51e1c3-eedf-40c9-895d-58cf0ecd1ffa&database=us_data HTTP/1.1" 200 -
2025-06-27 14:28:34,635 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 14:28:35,752 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 14:28:36,635 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 14:28:36,636 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 14:30:51,225 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a
2025-06-27 14:30:51,336 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:30:51,337 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 14:30:51,337 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 14:30:51,341 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:30:51,341 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 14:30:51,351 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 14:30:51,377 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a
2025-06-27 14:30:51,380 - __main__ - INFO - [m.py:410] - Connection stored for session 6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a: mysql/
2025-06-27 14:30:51,396 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:51] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:30:51,548 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:51] "GET /api/databases?session_id=6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a HTTP/1.1" 200 -
2025-06-27 14:30:56,643 - __main__ - INFO - [m.py:486] - Updated selected database for session 6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a: us_data
2025-06-27 14:30:56,645 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:56] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:30:56,773 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:56] "GET /api/tables?session_id=6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a&database=us_data HTTP/1.1" 200 -
2025-06-27 14:30:56,898 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:56] "GET /api/schema?session_id=6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a&database=us_data HTTP/1.1" 200 -
2025-06-27 14:34:44,975 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 86d27621-4744-4d06-b727-0b033d3562c9
2025-06-27 14:34:45,038 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 86d27621-4744-4d06-b727-0b033d3562c9
2025-06-27 14:34:45,040 - __main__ - INFO - [m.py:410] - Connection stored for session 86d27621-4744-4d06-b727-0b033d3562c9: mysql/
2025-06-27 14:34:45,056 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:45] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:34:45,359 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:45] "GET /api/databases?session_id=86d27621-4744-4d06-b727-0b033d3562c9 HTTP/1.1" 200 -
2025-06-27 14:34:48,113 - __main__ - INFO - [m.py:486] - Updated selected database for session 86d27621-4744-4d06-b727-0b033d3562c9: us_data
2025-06-27 14:34:48,114 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:48] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:34:48,209 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:48] "GET /api/tables?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=us_data HTTP/1.1" 200 -
2025-06-27 14:34:48,294 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:48] "GET /api/schema?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=us_data HTTP/1.1" 200 -
2025-06-27 14:35:49,004 - __main__ - INFO - [m.py:486] - Updated selected database for session 86d27621-4744-4d06-b727-0b033d3562c9: me
2025-06-27 14:35:49,006 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:35:49] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:35:49,095 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:35:49] "GET /api/tables?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=me HTTP/1.1" 200 -
2025-06-27 14:35:49,225 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:35:49] "GET /api/schema?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=me HTTP/1.1" 200 -
2025-06-27 14:38:15,612 - __main__ - INFO - [m.py:486] - Updated selected database for session 86d27621-4744-4d06-b727-0b033d3562c9: us_data
2025-06-27 14:38:15,618 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:38:15] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:38:15,743 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:38:15] "GET /api/tables?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=us_data HTTP/1.1" 200 -
2025-06-27 14:38:15,886 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:38:15] "GET /api/schema?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=us_data HTTP/1.1" 200 -
2025-06-27 14:40:58,741 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:40:58] "GET /api/table-data?session_id=6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a&table=employees&limit=5 HTTP/1.1" 200 -
2025-06-27 14:42:58,550 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 14:42:58,551 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-27 14:42:58,553 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 14:42:58,983 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 14:42:58,984 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 14:43:30,075 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session cb09f43c-ee25-4369-904d-56cef8eeb077
2025-06-27 14:43:30,162 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:43:30,163 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 14:43:30,164 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 14:43:30,174 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:43:30,175 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 14:43:30,180 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 14:43:30,211 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session cb09f43c-ee25-4369-904d-56cef8eeb077
2025-06-27 14:43:30,211 - __main__ - INFO - [m.py:410] - Connection stored for session cb09f43c-ee25-4369-904d-56cef8eeb077: mysql/
2025-06-27 14:43:30,215 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:30] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:43:30,440 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:30] "GET /api/databases?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077 HTTP/1.1" 200 -
2025-06-27 14:43:33,917 - __main__ - INFO - [m.py:486] - Updated selected database for session cb09f43c-ee25-4369-904d-56cef8eeb077: us_data
2025-06-27 14:43:33,918 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:33] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:43:33,996 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:33] "GET /api/tables?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&database=us_data HTTP/1.1" 200 -
2025-06-27 14:43:34,107 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:34] "GET /api/schema?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&database=us_data HTTP/1.1" 200 -
2025-06-27 14:44:48,320 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:44:48] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=employees&limit=5 HTTP/1.1" 200 -
2025-06-27 14:44:49,658 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:44:49] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=employees&limit=5 HTTP/1.1" 200 -
2025-06-27 14:44:50,558 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:44:50] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=ji&limit=5 HTTP/1.1" 200 -
2025-06-27 14:44:51,458 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:44:51] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 14:45:08,057 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:45:08] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 14:52:44,011 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:52:44] "[33mPOST /api/ai-query HTTP/1.1[0m" 404 -
2025-06-27 15:05:39,721 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session b6df9260-e1da-44fe-a505-239873b2b9a6
2025-06-27 15:05:39,955 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session b6df9260-e1da-44fe-a505-239873b2b9a6
2025-06-27 15:05:39,958 - __main__ - INFO - [m.py:410] - Connection stored for session b6df9260-e1da-44fe-a505-239873b2b9a6: mysql/
2025-06-27 15:05:39,975 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:39] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 15:05:40,462 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:40] "GET /api/databases?session_id=b6df9260-e1da-44fe-a505-239873b2b9a6 HTTP/1.1" 200 -
2025-06-27 15:05:42,428 - __main__ - INFO - [m.py:486] - Updated selected database for session b6df9260-e1da-44fe-a505-239873b2b9a6: us_data
2025-06-27 15:05:42,429 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:42] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 15:05:42,570 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:42] "GET /api/tables?session_id=b6df9260-e1da-44fe-a505-239873b2b9a6&database=us_data HTTP/1.1" 200 -
2025-06-27 15:05:42,669 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:42] "GET /api/schema?session_id=b6df9260-e1da-44fe-a505-239873b2b9a6&database=us_data HTTP/1.1" 200 -
2025-06-27 15:05:46,985 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:46] "GET /api/table-data?session_id=b6df9260-e1da-44fe-a505-239873b2b9a6&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 15:06:02,115 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:06:02] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 15:08:04,454 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:08:06,887 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:08:08,616 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:08:08,617 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 15:08:10,685 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:08:10,830 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:08:11,327 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:08:11,328 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 15:08:31,508 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session e11e9596-699e-4065-a664-08321397ae16
2025-06-27 15:08:31,612 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 15:08:31,613 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 15:08:31,614 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 15:08:31,621 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 15:08:31,623 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 15:08:31,631 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 15:08:31,685 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session e11e9596-699e-4065-a664-08321397ae16
2025-06-27 15:08:31,687 - __main__ - INFO - [m.py:410] - Connection stored for session e11e9596-699e-4065-a664-08321397ae16: mysql/
2025-06-27 15:08:31,690 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:31] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 15:08:31,922 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:31] "GET /api/databases?session_id=e11e9596-699e-4065-a664-08321397ae16 HTTP/1.1" 200 -
2025-06-27 15:08:34,772 - __main__ - INFO - [m.py:486] - Updated selected database for session e11e9596-699e-4065-a664-08321397ae16: us_data
2025-06-27 15:08:34,773 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:34] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 15:08:34,931 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:34] "GET /api/tables?session_id=e11e9596-699e-4065-a664-08321397ae16&database=us_data HTTP/1.1" 200 -
2025-06-27 15:08:35,076 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:35] "GET /api/schema?session_id=e11e9596-699e-4065-a664-08321397ae16&database=us_data HTTP/1.1" 200 -
2025-06-27 15:08:47,492 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:47] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 15:11:20,209 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:11:20,981 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:11:22,733 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:11:22,739 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 15:11:34,940 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:11:35,077 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:11:35,946 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:11:35,948 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 15:12:26,218 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session da8fc6ae-19a8-475b-8c66-89d102804d84
2025-06-27 15:12:26,305 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 15:12:26,305 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 15:12:26,309 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 15:12:26,315 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 15:12:26,316 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 15:12:26,321 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 15:12:26,356 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session da8fc6ae-19a8-475b-8c66-89d102804d84
2025-06-27 15:12:26,359 - __main__ - INFO - [m.py:410] - Connection stored for session da8fc6ae-19a8-475b-8c66-89d102804d84: mysql/
2025-06-27 15:12:26,361 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:26] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 15:12:26,586 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:26] "GET /api/databases?session_id=da8fc6ae-19a8-475b-8c66-89d102804d84 HTTP/1.1" 200 -
2025-06-27 15:12:28,952 - __main__ - INFO - [m.py:486] - Updated selected database for session da8fc6ae-19a8-475b-8c66-89d102804d84: us_data
2025-06-27 15:12:28,954 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:28] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 15:12:29,063 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:29] "GET /api/tables?session_id=da8fc6ae-19a8-475b-8c66-89d102804d84&database=us_data HTTP/1.1" 200 -
2025-06-27 15:12:29,173 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:29] "GET /api/schema?session_id=da8fc6ae-19a8-475b-8c66-89d102804d84&database=us_data HTTP/1.1" 200 -
2025-06-27 15:12:44,325 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:44] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 15:12:48,283 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:48] "GET /api/table-data?session_id=da8fc6ae-19a8-475b-8c66-89d102804d84&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 15:16:15,106 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session c3429d36-341c-41fd-8dc3-8bd79d1946db
2025-06-27 15:16:15,142 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session c3429d36-341c-41fd-8dc3-8bd79d1946db
2025-06-27 15:16:15,145 - __main__ - INFO - [m.py:410] - Connection stored for session c3429d36-341c-41fd-8dc3-8bd79d1946db: mysql/
2025-06-27 15:16:15,147 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:15] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 15:16:15,381 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:15] "GET /api/databases?session_id=c3429d36-341c-41fd-8dc3-8bd79d1946db HTTP/1.1" 200 -
2025-06-27 15:16:18,021 - __main__ - INFO - [m.py:486] - Updated selected database for session c3429d36-341c-41fd-8dc3-8bd79d1946db: us_data
2025-06-27 15:16:18,022 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:18] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 15:16:18,103 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:18] "GET /api/tables?session_id=c3429d36-341c-41fd-8dc3-8bd79d1946db&database=us_data HTTP/1.1" 200 -
2025-06-27 15:16:18,179 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:18] "GET /api/schema?session_id=c3429d36-341c-41fd-8dc3-8bd79d1946db&database=us_data HTTP/1.1" 200 -
2025-06-27 15:16:31,445 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:31] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 15:19:42,490 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:19:43,922 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:19:45,084 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:19:45,084 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 17:08:45,591 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a
2025-06-27 17:08:45,830 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 17:08:45,832 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 17:08:45,838 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 17:08:45,848 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 17:08:45,849 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 17:08:45,887 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 17:08:45,944 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a
2025-06-27 17:08:45,946 - __main__ - INFO - [m.py:410] - Connection stored for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a: mysql/
2025-06-27 17:08:45,972 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:45] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 17:08:46,185 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:46] "GET /api/databases?session_id=5c2497a0-21cb-45db-ab9c-c6f771730a2a HTTP/1.1" 200 -
2025-06-27 17:08:49,965 - __main__ - INFO - [m.py:486] - Updated selected database for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a: us_data
2025-06-27 17:08:49,966 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:49] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 17:08:50,127 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:50] "GET /api/tables?session_id=5c2497a0-21cb-45db-ab9c-c6f771730a2a&database=us_data HTTP/1.1" 200 -
2025-06-27 17:08:50,240 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:50] "GET /api/schema?session_id=5c2497a0-21cb-45db-ab9c-c6f771730a2a&database=us_data HTTP/1.1" 200 -
2025-06-27 17:08:57,231 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:57] "GET /api/table-data?session_id=5c2497a0-21cb-45db-ab9c-c6f771730a2a&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 17:19:04,466 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:19:04] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 18:10:14,943 - __main__ - INFO - [m.py:439] - Cleaning up expired session: 5c2497a0-21cb-45db-ab9c-c6f771730a2a
2025-06-27 18:10:14,975 - __main__ - INFO - [m.py:478] - Connection removed for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a
2025-06-27 22:25:03,825 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 22:25:03,826 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-27 22:25:03,827 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 22:25:04,279 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 22:25:04,374 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-27 22:25:24,729 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session e62596e8-f899-42d1-9125-301caa4893bd
2025-06-27 22:25:24,964 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 22:25:24,964 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 22:25:24,964 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 22:25:24,974 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 22:25:24,975 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 22:25:24,976 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 22:25:25,021 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session e62596e8-f899-42d1-9125-301caa4893bd
2025-06-27 22:25:25,021 - __main__ - INFO - [m.py:410] - Connection stored for session e62596e8-f899-42d1-9125-301caa4893bd: mysql/
2025-06-27 22:25:25,023 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:25] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 22:25:25,389 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:25] "GET /api/databases?session_id=e62596e8-f899-42d1-9125-301caa4893bd HTTP/1.1" 200 -
2025-06-27 22:25:27,738 - __main__ - INFO - [m.py:486] - Updated selected database for session e62596e8-f899-42d1-9125-301caa4893bd: us_data
2025-06-27 22:25:27,739 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:27] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 22:25:27,814 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:27] "GET /api/tables?session_id=e62596e8-f899-42d1-9125-301caa4893bd&database=us_data HTTP/1.1" 200 -
2025-06-27 22:25:28,000 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:27] "GET /api/schema?session_id=e62596e8-f899-42d1-9125-301caa4893bd&database=us_data HTTP/1.1" 200 -
2025-06-27 22:26:06,414 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:06] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:26:09,291 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:09] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 22:26:12,204 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:12] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:26:20,241 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:20] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=ji&limit=5 HTTP/1.1" 200 -
2025-06-27 22:26:22,652 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:22] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=employees&limit=5 HTTP/1.1" 200 -
2025-06-27 22:26:24,038 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:24] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 22:26:31,950 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:31] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:26:41,864 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:41] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:34:32,208 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:34:32] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:34:42,301 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:34:42] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:34:53,902 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:34:53] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 22:38:00,518 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session ace5be2c-6161-495d-8c6c-6268d8d09eab
2025-06-27 22:38:00,606 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session ace5be2c-6161-495d-8c6c-6268d8d09eab
2025-06-27 22:38:00,609 - __main__ - INFO - [m.py:410] - Connection stored for session ace5be2c-6161-495d-8c6c-6268d8d09eab: mysql/
2025-06-27 22:38:00,610 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:00] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 22:38:00,982 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:00] "GET /api/databases?session_id=ace5be2c-6161-495d-8c6c-6268d8d09eab HTTP/1.1" 200 -
2025-06-27 22:38:01,979 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:01] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:38:05,660 - __main__ - INFO - [m.py:486] - Updated selected database for session ace5be2c-6161-495d-8c6c-6268d8d09eab: us_data
2025-06-27 22:38:05,664 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:05] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 22:38:05,767 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:05] "GET /api/tables?session_id=ace5be2c-6161-495d-8c6c-6268d8d09eab&database=us_data HTTP/1.1" 200 -
2025-06-27 22:38:05,842 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:05] "GET /api/schema?session_id=ace5be2c-6161-495d-8c6c-6268d8d09eab&database=us_data HTTP/1.1" 200 -
2025-06-27 22:38:07,105 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:07] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 23:35:05,772 - __main__ - INFO - [m.py:439] - Cleaning up expired session: e62596e8-f899-42d1-9125-301caa4893bd
2025-06-27 23:35:05,801 - __main__ - INFO - [m.py:478] - Connection removed for session e62596e8-f899-42d1-9125-301caa4893bd
2025-06-27 23:40:05,907 - __main__ - INFO - [m.py:439] - Cleaning up expired session: ace5be2c-6161-495d-8c6c-6268d8d09eab
2025-06-27 23:40:05,915 - __main__ - INFO - [m.py:478] - Connection removed for session ace5be2c-6161-495d-8c6c-6268d8d09eab
2025-06-27 23:42:36,597 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 23:42:37,964 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 23:42:39,342 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 23:42:39,344 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-27 23:49:37,724 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 9a6b6375-492c-4d35-bcf3-7b4e477aae77
2025-06-27 23:49:37,855 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 23:49:37,856 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 23:49:37,857 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 23:49:37,864 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 23:49:37,864 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 23:49:37,869 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 23:49:37,916 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 9a6b6375-492c-4d35-bcf3-7b4e477aae77
2025-06-27 23:49:37,919 - __main__ - INFO - [m.py:410] - Connection stored for session 9a6b6375-492c-4d35-bcf3-7b4e477aae77: mysql/
2025-06-27 23:49:37,923 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:49:37] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 23:49:38,197 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:49:38] "GET /api/databases?session_id=9a6b6375-492c-4d35-bcf3-7b4e477aae77 HTTP/1.1" 200 -
2025-06-27 23:49:55,139 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 7a3c9a76-21f3-4091-9aff-6b4b33a53006
2025-06-27 23:49:55,148 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 7a3c9a76-21f3-4091-9aff-6b4b33a53006
2025-06-27 23:49:55,149 - __main__ - INFO - [m.py:410] - Connection stored for session 7a3c9a76-21f3-4091-9aff-6b4b33a53006: mysql/
2025-06-27 23:49:55,155 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:49:55] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 23:49:55,385 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:49:55] "GET /api/databases?session_id=7a3c9a76-21f3-4091-9aff-6b4b33a53006 HTTP/1.1" 200 -
2025-06-27 23:50:00,997 - __main__ - INFO - [m.py:486] - Updated selected database for session 7a3c9a76-21f3-4091-9aff-6b4b33a53006: us_data
2025-06-27 23:50:00,999 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:50:00] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 23:50:01,137 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:50:01] "GET /api/tables?session_id=7a3c9a76-21f3-4091-9aff-6b4b33a53006&database=us_data HTTP/1.1" 200 -
2025-06-27 23:50:01,243 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:50:01] "GET /api/schema?session_id=7a3c9a76-21f3-4091-9aff-6b4b33a53006&database=us_data HTTP/1.1" 200 -
2025-06-27 23:50:27,449 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:50:27] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-27 23:54:11,164 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 23:54:12,432 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 23:54:13,836 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 23:54:13,838 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-27 23:58:24,521 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 2b62b4ad-2ee9-404e-8cc8-448567829c5a
2025-06-27 23:58:24,695 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 23:58:24,695 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 23:58:24,695 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 23:58:24,701 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 23:58:24,702 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 23:58:24,711 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 23:58:24,787 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 2b62b4ad-2ee9-404e-8cc8-448567829c5a
2025-06-27 23:58:24,789 - __main__ - INFO - [m.py:410] - Connection stored for session 2b62b4ad-2ee9-404e-8cc8-448567829c5a: mysql/
2025-06-27 23:58:24,791 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:24] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 23:58:25,033 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:25] "GET /api/databases?session_id=2b62b4ad-2ee9-404e-8cc8-448567829c5a HTTP/1.1" 200 -
2025-06-27 23:58:29,371 - __main__ - INFO - [m.py:486] - Updated selected database for session 2b62b4ad-2ee9-404e-8cc8-448567829c5a: us_data
2025-06-27 23:58:29,374 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:29] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 23:58:29,590 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:29] "GET /api/tables?session_id=2b62b4ad-2ee9-404e-8cc8-448567829c5a&database=us_data HTTP/1.1" 200 -
2025-06-27 23:58:29,829 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:29] "GET /api/schema?session_id=2b62b4ad-2ee9-404e-8cc8-448567829c5a&database=us_data HTTP/1.1" 200 -
2025-06-27 23:58:48,201 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:48] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 00:23:38,755 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 00:23:40,210 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 00:29:17,477 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-28 00:29:17,478 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-28 00:29:17,484 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 00:29:17,936 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 00:29:17,936 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 00:33:13,522 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 032809c9-8289-4ac8-b02d-f50ec0649dd4
2025-06-28 00:33:13,656 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 00:33:13,657 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 00:33:13,657 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 00:33:13,660 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 00:33:13,661 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 00:33:13,663 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 00:33:13,695 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 032809c9-8289-4ac8-b02d-f50ec0649dd4
2025-06-28 00:33:13,697 - __main__ - INFO - [m.py:410] - Connection stored for session 032809c9-8289-4ac8-b02d-f50ec0649dd4: mysql/
2025-06-28 00:33:13,699 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:13] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 00:33:13,967 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:13] "GET /api/databases?session_id=032809c9-8289-4ac8-b02d-f50ec0649dd4 HTTP/1.1" 200 -
2025-06-28 00:33:16,631 - __main__ - INFO - [m.py:486] - Updated selected database for session 032809c9-8289-4ac8-b02d-f50ec0649dd4: us_data
2025-06-28 00:33:16,634 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:16] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 00:33:16,760 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:16] "GET /api/tables?session_id=032809c9-8289-4ac8-b02d-f50ec0649dd4&database=us_data HTTP/1.1" 200 -
2025-06-28 00:33:16,900 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:16] "GET /api/schema?session_id=032809c9-8289-4ac8-b02d-f50ec0649dd4&database=us_data HTTP/1.1" 200 -
